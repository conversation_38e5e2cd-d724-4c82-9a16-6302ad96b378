{"miniprogramRoot": "dist/", "projectname": "mozi_we<PERSON>tapp", "description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "appid": "wx6738949cc8dcd21b", "setting": {"urlCheck": true, "es6": true, "enhance": true, "compileHotReLoad": false, "postcss": false, "minified": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "coverView": true, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "uglifyFileName": false, "uploadWithSourceMap": true, "useMultiFrameRuntime": true, "showShadowRootInWxmlPanel": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "useStaticServer": true, "showES6CompileOption": false, "checkInvalidKey": true, "disableUseStrict": false, "useCompilerPlugins": false, "minifyWXML": true}, "compileType": "miniprogram", "libVersion": "latest", "srcMiniprogramRoot": "dist/", "packOptions": {"ignore": [], "include": []}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}