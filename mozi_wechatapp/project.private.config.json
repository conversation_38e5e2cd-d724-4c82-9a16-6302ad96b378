{"description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "projectname": "mozi_we<PERSON>tapp", "setting": {"compileHotReLoad": true}, "libVersion": "latest", "condition": {"miniprogram": {"list": [{"name": "community/index", "pathName": "pages/community/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/commentinfo/index", "pathName": "pages/commentinfo/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/user/index", "pathName": "pages/user/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/mywarn/index", "pathName": "pages/mywarn/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/addwarn/index", "pathName": "pages/addwarn/index", "query": "symbol=btc", "launchMode": "default", "scene": null}, {"name": "pages/landscapechart/index", "pathName": "pages/landscapechart/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/fundingrate/index", "pathName": "pages/fundingrate/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/positionsize/index", "pathName": "pages/positionsize/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/search/index", "pathName": "pages/search/index", "query": "", "launchMode": "default", "scene": null}, {"name": "find/index", "pathName": "pages/find/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/putcallratio/index", "pathName": "pages/putcallratio/index", "query": "", "launchMode": "default", "scene": 1011}]}}}