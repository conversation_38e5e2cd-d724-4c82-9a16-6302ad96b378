/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconJiaoyichaxun = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M937.8816 897.2288l-135.7824-126.976c67.5328-73.1136 108.9024-170.752 108.9024-277.8624 0-226.048-183.9104-409.9072-409.9072-409.9072S91.136 266.3424 91.136 492.3904s183.9104 409.9072 409.9072 409.9072c100.4544 0 192.5632-36.4032 263.936-96.6144l137.8816 128.9216c4.9152 4.608 11.2128 6.912 17.4592 6.912 6.8608 0 13.6704-2.7136 18.688-8.0896 9.728-10.3424 9.216-26.5216-1.1264-36.1984zM142.336 492.3904c0-197.7856 160.9216-358.7072 358.7072-358.7072s358.7072 160.9216 358.7072 358.7072-160.9216 358.7072-358.7072 358.7072S142.336 690.176 142.336 492.3904z"
        fill={getIconColor(color, 0, '#231815')}
      />
      <Path
        d="M648.704 541.7472h-122.0608V461.312h122.0608c14.1312 0 25.6-11.4688 25.6-25.6s-11.4688-25.6-25.6-25.6h-100.1472l77.824-72.0896a25.58976 25.58976 0 0 0 1.3824-36.1984 25.58976 25.58976 0 0 0-36.1984-1.3824L500.224 385.1264l-89.5488-84.48a25.63072 25.63072 0 0 0-36.1984 1.0752 25.63072 25.63072 0 0 0 1.0752 36.1984L452.096 410.112H353.3824c-14.1312 0-25.6 11.4688-25.6 25.6s11.4688 25.6 25.6 25.6h122.0608v80.4352H353.3824c-14.1312 0-25.6 11.4688-25.6 25.6s11.4688 25.6 25.6 25.6h122.0608v84.3264c0 14.1312 11.4688 25.6 25.6 25.6s25.6-11.4688 25.6-25.6v-84.3264h122.0608c14.1312 0 25.6-11.4688 25.6-25.6s-11.4688-25.6-25.6-25.6z"
        fill={getIconColor(color, 1, '#FF3355')}
      />
    </Svg>
  );
};

IconJiaoyichaxun.defaultProps = {
  size: 24,
};

IconJiaoyichaxun = React.memo ? React.memo(IconJiaoyichaxun) : IconJiaoyichaxun;

export default IconJiaoyichaxun;
