/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconLicaichanpin2 = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M503.552 499.456c-9.3184 0-18.5856-2.2016-27.0848-6.5536L182.016 340.736c-18.688-9.6256-30.3104-28.3648-30.3616-48.8448-0.0512-20.48 11.52-39.2704 30.1568-48.9984l301.2096-157.184c16.9472-8.8576 37.2224-8.9088 54.2208-0.1536l301.0048 154.8288c18.8928 9.728 30.5664 28.6208 30.464 49.3056-0.1024 20.6848-11.9808 39.4752-30.976 48.9984l-307.7632 154.5216c-8.2944 4.1984-17.3568 6.2464-26.4192 6.2464zM205.5168 288.3072c-1.7408 0.9216-2.6112 2.048-2.6112 3.4816s0.8704 2.56 2.6624 3.4816l294.4512 152.1664c2.1504 1.1264 4.9152 1.1264 7.0144 0.0512l307.7632-154.5216c1.8432-0.9216 2.7136-2.048 2.7136-3.5328 0-1.4336-0.8704-2.56-2.6624-3.5328L513.8432 131.072c-2.1504-1.1264-4.9664-1.0752-7.1168 0L205.5168 288.3072zM415.488 942.6432c-9.8304 0-19.6608-2.4576-28.4672-7.3216l-275.5072-152.3712c-17.8176-9.8304-28.8768-28.3136-28.8768-48.128l-0.3072-286.5664c0-19.2512 10.0352-36.864 26.88-47.104 17.7664-10.752 39.424-11.4688 57.8048-1.792l275.8144 144.1792c18.5856 9.728 30.1056 28.416 30.1056 48.8448v294.7072c0 19.5072-10.2912 37.2224-27.4944 47.4112-9.216 5.4272-19.5584 8.1408-29.952 8.1408zM139.7248 443.8528c-1.9456 0-3.4304 0.768-3.9936 1.1264-2.2016 1.3312-2.2016 2.7648-2.2016 3.2768l0.3072 286.5664c0 1.28 0.9728 2.56 2.4576 3.3792l275.5072 152.3712c3.584 1.9968 6.7584 0.4096 7.6288-0.1024 2.304-1.3312 2.304-2.816 2.304-3.2768V592.384c0-1.28-1.024-2.6112-2.6112-3.4816L143.2576 444.7232c-1.28-0.6656-2.4576-0.8704-3.5328-0.8704z"
        fill={getIconColor(color, 0, '#231815')}
      />
      <Path
        d="M601.4464 942.6432c-10.3936 0-20.736-2.7136-29.952-8.1408-17.2032-10.1376-27.4944-27.8528-27.4944-47.4112V592.384c0-20.4288 11.52-39.1168 30.1056-48.8448L849.92 399.36c18.432-9.6256 40.0384-8.96 57.8048 1.792 16.8448 10.24 26.88 27.8016 26.88 47.104l-0.3072 286.5664c0 19.8656-11.1104 38.2976-28.8768 48.128l-275.5072 152.3712c-8.8064 4.864-18.6368 7.3216-28.4672 7.3216z m275.7632-498.7904c-1.0752 0-2.304 0.256-3.5328 0.8704l-275.8144 144.1792c-1.5872 0.8704-2.6112 2.2016-2.6112 3.4816v294.7072c0 0.4608 0 1.9456 2.304 3.2768 0.8704 0.512 4.0448 2.0992 7.6288 0.1024l275.456-152.32c1.536-0.8192 2.4576-2.1504 2.4576-3.3792l0.3072-286.5664c0-0.4608 0-1.8944-2.2016-3.2768-0.5632-0.3072-2.048-1.0752-3.9936-1.0752z m-15.4112-21.8112z"
        fill={getIconColor(color, 1, '#FF3355')}
      />
    </Svg>
  );
};

IconLicaichanpin2.defaultProps = {
  size: 24,
};

IconLicaichanpin2 = React.memo ? React.memo(IconLicaichanpin2) : IconLicaichanpin2;

export default IconLicaichanpin2;
