/* eslint-disable */

import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { getIconColor } from './helper';

let IconJifen = ({ size, color, ...rest }) => {
  return (
    <Svg viewBox="0 0 1024 1024" width={size} height={size} {...rest}>
      <Path
        d="M512 472.1664c-243.2 0-426.5984-81.2032-426.5984-188.928S268.8 94.3616 512 94.3616c243.2 0 426.5984 81.2032 426.5984 188.928S755.2 472.1664 512 472.1664z m0-326.6048c-221.2352 0-375.3984 72.5504-375.3984 137.728 0 65.1264 154.1632 137.728 375.3984 137.728s375.3984-72.5504 375.3984-137.728c0-65.1776-154.1632-137.728-375.3984-137.728z"
        fill={getIconColor(color, 0, '#231815')}
      />
      <Path
        d="M479.5392 373.6064c-76.5952 0-171.8272-15.4112-229.7856-52.736a25.57952 25.57952 0 0 1-7.6288-35.3792 25.57952 25.57952 0 0 1 35.3792-7.6288c54.016 34.816 160.256 48.4352 232.7552 43.5712 14.0288-0.8704 26.3168 9.728 27.2384 23.8592 0.9216 14.08-9.728 26.3168-23.8592 27.2384-10.7008 0.7168-22.1696 1.0752-34.0992 1.0752z"
        fill={getIconColor(color, 1, '#FF3355')}
      />
      <Path
        d="M512 699.8016c-243.2 0-426.5984-81.2032-426.5984-188.928 0-25.6512 10.24-50.1248 30.5152-72.8064a25.6 25.6 0 1 1 38.1952 34.0992c-11.6224 13.0048-17.5104 26.0608-17.5104 38.7584 0 65.1264 154.1632 137.728 375.3984 137.728s375.3984-72.5504 375.3984-137.728c0-13.568-6.7072-27.4944-19.9168-41.3696-9.728-10.24-9.3696-26.4704 0.8704-36.1984 10.24-9.7792 26.4704-9.3696 36.1984 0.8704 22.5792 23.7056 34.048 49.5104 34.048 76.6976 0 107.6736-183.3984 188.8768-426.5984 188.8768z"
        fill={getIconColor(color, 2, '#231815')}
      />
      <Path
        d="M512 929.6384c-243.2 0-426.5984-81.2032-426.5984-188.928 0-25.6512 10.24-50.1248 30.5152-72.8064a25.6 25.6 0 1 1 38.1952 34.0992c-11.6224 13.0048-17.5104 26.0608-17.5104 38.7584 0 65.1264 154.1632 137.728 375.3984 137.728s375.3984-72.5504 375.3984-137.728c0-13.568-6.7072-27.4944-19.9168-41.3696-9.728-10.24-9.3696-26.4704 0.8704-36.1984 10.24-9.7792 26.4704-9.3696 36.1984 0.8704 22.5792 23.7056 34.048 49.5104 34.048 76.6976 0 107.6736-183.3984 188.8768-426.5984 188.8768z"
        fill={getIconColor(color, 3, '#231815')}
      />
    </Svg>
  );
};

IconJifen.defaultProps = {
  size: 24,
};

IconJifen = React.memo ? React.memo(IconJifen) : IconJifen;

export default IconJifen;
