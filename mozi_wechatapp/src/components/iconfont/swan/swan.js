Component({
  properties: {
    // check | shequ | message | ellipsis | bell-fill | arrawsalt | close | info-circle | plus-square | moneycollect | attachment | share | wechat-fill | m-pingfen | info-circle-fill | file-copy | caret-down | caret-up | bodongfenxi | jijin | jiaoyichaxun | jifen | wangdian | piaowu | licaichanpin2 | licaichanpin | heart-fill | close-circle-fill | search | right
    name: {
      type: String,
    },
    // string | string[]
    color: {
      type: null,
      value: '',
      observer: function(color) {
        this.setData({
          colors: this.fixColor(color),
          isStr: typeof color === 'string',
        });
      }
    },
    size: {
      type: Number,
      value: 24,
      observer: function(size) {
        this.setData({
          svgSize: size / 750 * swan.getSystemInfoSync().windowWidth,
        });
      },
    },
  },
  data: {
    colors: '',
    svgSize: 24 / 750 * swan.getSystemInfoSync().windowWidth,
    quot: '"',
    isStr: true,
  },
  methods: {
    fixColor: function() {
      var color = this.data.color;
      var hex2rgb = this.hex2rgb;

      if (typeof color === 'string') {
        return color.indexOf('#') === 0 ? hex2rgb(color) : color;
      }

      return color.map(function (item) {
        return item.indexOf('#') === 0 ? hex2rgb(item) : item;
      });
    },
    hex2rgb: function(hex) {
      var rgb = [];

      hex = hex.substr(1);

      if (hex.length === 3) {
        hex = hex.replace(/(.)/g, '$1$1');
      }

      hex.replace(/../g, function(color) {
        rgb.push(parseInt(color, 0x10));
        return color;
      });

      return 'rgb(' + rgb.join(',') + ')';
    }
  }
});
