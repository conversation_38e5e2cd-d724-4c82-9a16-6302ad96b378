<!--check-->
<view tt:if="{{name === 'check'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474c-6.1-7.7-15.3-12.2-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1 0.4-12.8-6.3-12.8z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--shequ-->
<view tt:if="{{name === 'shequ'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 85.333333c235.637333 0 426.666667 191.029333 426.666667 426.666667S747.637333 938.666667 512 938.666667 85.333333 747.637333 85.333333 512 276.362667 85.333333 512 85.333333z m143.381333 497.781334A159.978667 159.978667 0 0 1 512 672a159.978667 159.978667 0 0 1-143.36-88.853333 32 32 0 1 0-57.301333 28.490666A223.968 223.968 0 0 0 512 736a223.968 223.968 0 0 0 200.682667-124.394667 32 32 0 0 0-57.301334-28.490666z' fill='{{(isStr ? colors : colors[0]) || 'rgb(0,0,0)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--message-->
<view tt:if="{{name === 'message'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 512m-48 0a48 48 0 1 0 96 0 48 48 0 1 0-96 0Z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M712 512m-48 0a48 48 0 1 0 96 0 48 48 0 1 0-96 0Z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M312 512m-48 0a48 48 0 1 0 96 0 48 48 0 1 0-96 0Z' fill='{{(isStr ? colors : colors[2]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M925.2 338.4c-22.6-53.7-55-101.9-96.3-143.3-41.3-41.3-89.5-73.8-143.3-96.3C630.6 75.7 572.2 64 512 64h-2c-60.6 0.3-119.3 12.3-174.5 35.9-53.3 22.8-101.1 55.2-142 96.5-40.9 41.3-73 89.3-95.2 142.8-23 55.4-34.6 114.3-34.3 174.9 0.3 69.4 16.9 138.3 48 199.9v152c0 25.4 20.6 46 46 46h152.1c61.6 31.1 130.5 47.7 199.9 48h2.1c59.9 0 118-11.6 172.7-34.3 53.5-22.3 101.6-54.3 142.8-95.2 41.3-40.9 73.8-88.7 96.5-142 23.6-55.2 35.6-113.9 35.9-174.5 0.3-60.9-11.5-120-34.8-175.6z m-151.1 438C704 845.8 611 884 512 884h-1.7c-60.3-0.3-120.2-15.3-173.1-43.5l-8.4-4.5H188V695.2l-4.5-8.4C155.3 633.9 140.3 574 140 513.7c-0.4-99.7 37.7-193.3 107.6-263.8 69.8-70.5 163.1-109.5 262.8-109.9h1.7c50 0 98.5 9.7 144.2 28.9 44.6 18.7 84.6 45.6 119 80 34.3 34.3 61.3 74.4 80 119 19.4 46.2 29.1 95.2 28.9 145.8-0.6 99.6-39.7 192.9-110.1 262.7z' fill='{{(isStr ? colors : colors[3]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--ellipsis-->
<view tt:if="{{name === 'ellipsis'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M232 511m-56 0a56 56 0 1 0 112 0 56 56 0 1 0-112 0Z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M512 511m-56 0a56 56 0 1 0 112 0 56 56 0 1 0-112 0Z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M792 511m-56 0a56 56 0 1 0 112 0 56 56 0 1 0-112 0Z' fill='{{(isStr ? colors : colors[2]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--bell-fill-->
<view tt:if="{{name === 'bell-fill'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M816 768h-24V428c0-141.1-104.3-257.8-240-277.2V112c0-22.1-17.9-40-40-40s-40 17.9-40 40v38.8C336.3 170.2 232 286.9 232 428v340h-24c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h216c0 61.8 50.2 112 112 112s112-50.2 112-112h216c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM512 888c-26.5 0-48-21.5-48-48h96c0 26.5-21.5 48-48 48z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--arrawsalt-->
<view tt:if="{{name === 'arrawsalt'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M855 160.1l-189.2 23.5c-6.6 0.8-9.3 8.8-4.7 13.5l54.7 54.7-153.5 153.5c-3.1 3.1-3.1 8.2 0 11.3l45.1 45.1c3.1 3.1 8.2 3.1 11.3 0l153.6-153.6 54.7 54.7c4.7 4.7 12.7 1.9 13.5-4.7L863.9 169c0.7-5.2-3.7-9.6-8.9-8.9zM416.6 562.3c-3.1-3.1-8.2-3.1-11.3 0L251.8 715.9l-54.7-54.7c-4.7-4.7-12.7-1.9-13.5 4.7L160.1 855c-0.6 5.2 3.7 9.5 8.9 8.9l189.2-23.5c6.6-0.8 9.3-8.8 4.7-13.5l-54.7-54.7 153.6-153.6c3.1-3.1 3.1-8.2 0-11.3l-45.2-45z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--close-->
<view tt:if="{{name === 'close'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M563.8 512l262.5-312.9c4.4-5.2 0.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8 295.1 191.7c-3-3.6-7.5-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512 196.9 824.9c-4.4 5.2-0.7 13.1 6.1 13.1h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1 216.5 258.1c3 3.6 7.5 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--info-circle-->
<view tt:if="{{name === 'info-circle'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M512 336m-48 0a48 48 0 1 0 96 0 48 48 0 1 0-96 0Z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M536 448h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V456c0-4.4-3.6-8-8-8z' fill='{{(isStr ? colors : colors[2]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--plus-square-->
<view tt:if="{{name === 'plus-square'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M328 544h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32z m-40 728H184V184h656v656z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--moneycollect-->
<view tt:if="{{name === 'moneycollect'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M911.5 700.7c-1.5-4.2-6.1-6.3-10.3-4.8L840 718.2V180c0-37.6-30.4-68-68-68H252c-37.6 0-68 30.4-68 68v538.2l-61.3-22.3c-0.9-0.3-1.8-0.5-2.7-0.5-4.4 0-8 3.6-8 8V763c0 3.3 2.1 6.3 5.3 7.5L501 910.1c7.1 2.6 14.8 2.6 21.9 0l383.8-139.5c3.2-1.2 5.3-4.2 5.3-7.5v-59.6c0-1-0.2-1.9-0.5-2.8zM512 837.5l-256-93.1V184h512v560.4l-256 93.1z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M660.6 312h-54.5c-3 0-5.8 1.7-7.1 4.4l-84.7 168.8H511l-84.7-168.8c-1.4-2.7-4.1-4.4-7.1-4.4h-55.7c-1.3 0-2.6 0.3-3.8 1-3.9 2.1-5.3 7-3.2 10.8l103.9 191.6h-57c-4.4 0-8 3.6-8 8v27.1c0 4.4 3.6 8 8 8h76v39h-76c-4.4 0-8 3.6-8 8v27.1c0 4.4 3.6 8 8 8h76V704c0 4.4 3.6 8 8 8h49.9c4.4 0 8-3.6 8-8v-63.5h76.3c4.4 0 8-3.6 8-8v-27.1c0-4.4-3.6-8-8-8h-76.3v-39h76.3c4.4 0 8-3.6 8-8v-27.1c0-4.4-3.6-8-8-8H564l103.7-191.6c0.6-1.2 1-2.5 1-3.8-0.1-4.3-3.7-7.9-8.1-7.9z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--attachment-->
<view tt:if="{{name === 'attachment'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M779.3 196.6c-94.2-94.2-247.6-94.2-341.7 0l-261 260.8c-1.7 1.7-2.6 4-2.6 6.4s0.9 4.7 2.6 6.4l36.9 36.9c3.5 3.5 9.2 3.5 12.7 0l261-260.8c32.4-32.4 75.5-50.2 121.3-50.2s88.9 17.8 121.2 50.2c32.4 32.4 50.2 75.5 50.2 121.2 0 45.8-17.8 88.8-50.2 121.2l-266 265.9-43.1 43.1c-40.3 40.3-105.8 40.3-146.1 0-19.5-19.5-30.2-45.4-30.2-73s10.7-53.5 30.2-73l263.9-263.8c6.7-6.6 15.5-10.3 24.9-10.3h0.1c9.4 0 18.1 3.7 24.7 10.3 6.7 6.7 10.3 15.5 10.3 24.9 0 9.3-3.7 18.1-10.3 24.7L372.4 653c-1.7 1.7-2.6 4-2.6 6.4s0.9 4.7 2.6 6.4l36.9 36.9c3.5 3.5 9.2 3.5 12.7 0l215.6-215.6c19.9-19.9 30.8-46.3 30.8-74.4s-11-54.6-30.8-74.4c-41.1-41.1-107.9-41-149 0L463 364 224.8 602.1C192 634.9 174 678.4 174 724.8c0 46.3 18.1 89.8 50.8 122.5 33.9 33.8 78.3 50.7 122.7 50.7 44.4 0 88.8-16.9 122.6-50.7l309.2-309C824.8 492.7 850 432 850 367.5c0.1-64.6-25.1-125.3-70.7-170.9z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--share-->
<view tt:if="{{name === 'share'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M752 664c-28.5 0-54.8 10-75.4 26.7L469.4 540.8c1.7-9.3 2.6-19 2.6-28.8s-0.9-19.4-2.6-28.8l207.2-149.9C697.2 350 723.5 360 752 360c66.2 0 120-53.8 120-120s-53.8-120-120-120-120 53.8-120 120c0 11.6 1.6 22.7 4.7 33.3L439.9 415.8C410.7 377.1 364.3 352 312 352c-88.4 0-160 71.6-160 160s71.6 160 160 160c52.3 0 98.7-25.1 127.9-63.8l196.8 142.5c-3.1 10.6-4.7 21.8-4.7 33.3 0 66.2 53.8 120 120 120s120-53.8 120-120-53.8-120-120-120z m0-476c28.7 0 52 23.3 52 52s-23.3 52-52 52-52-23.3-52-52 23.3-52 52-52zM312 600c-48.5 0-88-39.5-88-88s39.5-88 88-88 88 39.5 88 88-39.5 88-88 88z m440 236c-28.7 0-52-23.3-52-52s23.3-52 52-52 52 23.3 52 52-23.3 52-52 52z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--wechat-fill-->
<view tt:if="{{name === 'wechat-fill'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M690.1 377.4c5.9 0 11.8 0.2 17.6 0.5-24.4-128.7-158.3-227.1-319.9-227.1C209 150.8 64 271.4 64 420.2c0 81.1 43.6 154.2 111.9 203.6 5.5 3.9 9.1 10.3 9.1 17.6 0 2.4-0.5 4.6-1.1 6.9-5.5 20.3-14.2 52.8-14.6 54.3-0.7 2.6-1.7 5.2-1.7 7.9 0 5.9 4.8 10.8 10.8 10.8 2.3 0 4.2-0.9 6.2-2l70.9-40.9c5.3-3.1 11-5 17.2-5 3.2 0 6.4 0.5 9.5 1.4 33.1 9.5 68.8 14.8 105.7 14.8 6 0 11.9-0.1 17.8-0.4-7.1-21-10.9-43.1-10.9-66 0-135.8 132.2-245.8 295.3-245.8z m-194.3-86.5c23.8 0 43.2 19.3 43.2 43.1s-19.3 43.1-43.2 43.1c-23.8 0-43.2-19.3-43.2-43.1s19.4-43.1 43.2-43.1z m-215.9 86.2c-23.8 0-43.2-19.3-43.2-43.1s19.3-43.1 43.2-43.1 43.2 19.3 43.2 43.1-19.4 43.1-43.2 43.1z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M866.7 792.7c56.9-41.2 93.2-102 93.2-169.7 0-124-120.8-224.5-269.9-224.5-149 0-269.9 100.5-269.9 224.5S540.9 847.5 690 847.5c30.8 0 60.6-4.4 88.1-12.3 2.6-0.8 5.2-1.2 7.9-1.2 5.2 0 9.9 1.6 14.3 4.1l59.1 34c1.7 1 3.3 1.7 5.2 1.7 2.4 0 4.7-0.9 6.4-2.6 1.7-1.7 2.6-4 2.6-6.4 0-2.2-0.9-4.4-1.4-6.6-0.3-1.2-7.6-28.3-12.2-45.3-0.5-1.9-0.9-3.8-0.9-5.7 0.1-5.9 3.1-11.2 7.6-14.5zM600.2 587.2c-19.9 0-36-16.1-36-35.9 0-19.8 16.1-35.9 36-35.9s36 16.1 36 35.9c0 19.8-16.2 35.9-36 35.9z m179.9 0c-19.9 0-36-16.1-36-35.9 0-19.8 16.1-35.9 36-35.9s36 16.1 36 35.9c-0.1 19.8-16.2 35.9-36 35.9z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--m-pingfen-->
<view tt:if="{{name === 'm-pingfen'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M122.88 171.52h778.24c-9.216 0-16.384-7.168-16.384-16.384v713.728c0-9.216 7.168-16.384 16.384-16.384H122.88c9.216 0 16.384 7.168 16.384 16.384V155.648c0 8.192-6.656 15.872-16.384 15.872z m-32.768 684.544c0 26.112 20.992 47.104 47.104 47.104h750.08c26.112 0 47.104-20.992 47.104-47.104V167.936c0-26.112-20.992-47.104-47.104-47.104H137.216c-26.112 0-47.104 20.992-47.104 47.104v688.128z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M743.936 415.744l-131.584-19.456L552.96 276.48c-8.192-16.896-24.576-25.6-40.96-25.6v45.568l69.632 141.312L737.28 460.8l-112.64 111.104 26.112 155.648-138.752-73.216v51.712l117.76 61.952c33.28 17.408 72.192-10.752 66.048-48.128l-22.016-132.608 95.232-94.208c26.624-26.112 11.776-71.68-25.088-77.312zM442.368 437.76L286.72 460.8l112.64 111.104-26.112 155.648 138.752-73.216V296.448z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M373.248 727.552l26.112-155.648L286.72 460.8l155.136-23.04L512 296.448V250.88c-16.384 0-32.768 8.704-40.96 25.6L412.16 396.288l-131.584 19.456c-37.376 5.632-52.224 51.2-25.088 77.312l95.232 94.208-22.016 132.608c-6.144 37.376 32.768 65.536 66.048 48.128l117.76-61.952v-51.712l-139.264 73.216z' fill='{{(isStr ? colors : colors[2]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--info-circle-fill-->
<view tt:if="{{name === 'info-circle-fill'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m32 664c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272z m-32-344c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--file-copy-->
<view tt:if="{{name === 'file-copy'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3Cpath d='M704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z' fill='{{(isStr ? colors : colors[1]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--caret-down-->
<view tt:if="{{name === 'caret-down'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--caret-up-->
<view tt:if="{{name === 'caret-up'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--bodongfenxi-->
<view tt:if="{{name === 'bodongfenxi'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M790.8864 916.4288H230.0416c-70.6048 0-128-57.3952-128-128V227.584c0-70.6048 57.3952-128 128-128h560.8448c70.6048 0 128 57.3952 128 128v560.8448c0 70.5536-57.3952 128-128 128zM230.0416 150.784c-42.3424 0-76.8 34.4576-76.8 76.8v560.8448c0 42.3424 34.4576 76.8 76.8 76.8h560.8448c42.3424 0 76.8-34.4576 76.8-76.8V227.584c0-42.3424-34.4576-76.8-76.8-76.8H230.0416z' fill='{{(isStr ? colors : colors[0]) || 'rgb(35,24,21)'}}' /%3E%3Cpath d='M505.3952 730.368c-14.1312 0-25.6-11.4688-25.6-25.6V311.2448c0-14.1312 11.4688-25.6 25.6-25.6s25.6 11.4688 25.6 25.6v393.5232c0 14.1312-11.4688 25.6-25.6 25.6zM309.9648 730.368c-14.1312 0-25.6-11.4688-25.6-25.6v-161.6896c0-14.1312 11.4688-25.6 25.6-25.6s25.6 11.4688 25.6 25.6v161.6896c0 14.1312-11.4688 25.6-25.6 25.6zM700.8256 730.368c-14.1312 0-25.6-11.4688-25.6-25.6v-308.736c0-14.1312 11.4688-25.6 25.6-25.6s25.6 11.4688 25.6 25.6v308.6848c0 14.1824-11.4688 25.6512-25.6 25.6512z' fill='{{(isStr ? colors : colors[1]) || 'rgb(255,51,85)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--jijin-->
<view tt:if="{{name === 'jijin'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M879.0016 918.3744h-153.7024c-14.1312 0-25.6-11.4688-25.6-25.6V352.6656c0-14.1312 11.4688-25.6 25.6-25.6h153.7024c14.1312 0 25.6 11.4688 25.6 25.6v540.1088c0 14.1312-11.4688 25.6-25.6 25.6z m-128.1024-51.2h102.5024V378.2656h-102.5024v488.9088zM588.8512 918.3744H435.1488c-14.1312 0-25.6-11.4688-25.6-25.6V464.4352c0-14.1312 11.4688-25.6 25.6-25.6h153.7024c14.1312 0 25.6 11.4688 25.6 25.6v428.3392c0 14.1312-11.4688 25.6-25.6 25.6z m-128.1024-51.2h102.5024V490.0352H460.7488v377.1392zM298.7008 918.3744H144.9984c-14.1312 0-25.6-11.4688-25.6-25.6v-323.7376c0-14.1312 11.4688-25.6 25.6-25.6h153.7024c14.1312 0 25.6 11.4688 25.6 25.6v323.7376c0 14.1312-11.4176 25.6-25.6 25.6z m-128.1024-51.2h102.5024v-272.5376H170.5984v272.5376z' fill='{{(isStr ? colors : colors[0]) || 'rgb(35,24,21)'}}' /%3E%3Cpath d='M144.9984 433.9712a25.58976 25.58976 0 0 1-13.5168-47.36l276.224-170.752c7.2704-4.5056 16.2816-5.0688 24.064-1.536l130.9184 59.392 170.8032-116.8896h-35.584c-14.1312 0-25.6-11.4688-25.6-25.6s11.4688-25.6 25.6-25.6h118.3232c11.2128 0 21.1456 7.3216 24.4736 18.0224 3.328 10.7008-0.7168 22.3744-9.984 28.672L579.7376 324.096c-7.3728 5.0688-16.896 5.888-25.0368 2.2016l-131.584-59.7504-264.6528 163.584c-4.1984 2.6112-8.8576 3.84-13.4656 3.84z' fill='{{(isStr ? colors : colors[1]) || 'rgb(255,51,85)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--jiaoyichaxun-->
<view tt:if="{{name === 'jiaoyichaxun'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M937.8816 897.2288l-135.7824-126.976c67.5328-73.1136 108.9024-170.752 108.9024-277.8624 0-226.048-183.9104-409.9072-409.9072-409.9072S91.136 266.3424 91.136 492.3904s183.9104 409.9072 409.9072 409.9072c100.4544 0 192.5632-36.4032 263.936-96.6144l137.8816 128.9216c4.9152 4.608 11.2128 6.912 17.4592 6.912 6.8608 0 13.6704-2.7136 18.688-8.0896 9.728-10.3424 9.216-26.5216-1.1264-36.1984zM142.336 492.3904c0-197.7856 160.9216-358.7072 358.7072-358.7072s358.7072 160.9216 358.7072 358.7072-160.9216 358.7072-358.7072 358.7072S142.336 690.176 142.336 492.3904z' fill='{{(isStr ? colors : colors[0]) || 'rgb(35,24,21)'}}' /%3E%3Cpath d='M648.704 541.7472h-122.0608V461.312h122.0608c14.1312 0 25.6-11.4688 25.6-25.6s-11.4688-25.6-25.6-25.6h-100.1472l77.824-72.0896a25.58976 25.58976 0 0 0 1.3824-36.1984 25.58976 25.58976 0 0 0-36.1984-1.3824L500.224 385.1264l-89.5488-84.48a25.63072 25.63072 0 0 0-36.1984 1.0752 25.63072 25.63072 0 0 0 1.0752 36.1984L452.096 410.112H353.3824c-14.1312 0-25.6 11.4688-25.6 25.6s11.4688 25.6 25.6 25.6h122.0608v80.4352H353.3824c-14.1312 0-25.6 11.4688-25.6 25.6s11.4688 25.6 25.6 25.6h122.0608v84.3264c0 14.1312 11.4688 25.6 25.6 25.6s25.6-11.4688 25.6-25.6v-84.3264h122.0608c14.1312 0 25.6-11.4688 25.6-25.6s-11.4688-25.6-25.6-25.6z' fill='{{(isStr ? colors : colors[1]) || 'rgb(255,51,85)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--jifen-->
<view tt:if="{{name === 'jifen'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 472.1664c-243.2 0-426.5984-81.2032-426.5984-188.928S268.8 94.3616 512 94.3616c243.2 0 426.5984 81.2032 426.5984 188.928S755.2 472.1664 512 472.1664z m0-326.6048c-221.2352 0-375.3984 72.5504-375.3984 137.728 0 65.1264 154.1632 137.728 375.3984 137.728s375.3984-72.5504 375.3984-137.728c0-65.1776-154.1632-137.728-375.3984-137.728z' fill='{{(isStr ? colors : colors[0]) || 'rgb(35,24,21)'}}' /%3E%3Cpath d='M479.5392 373.6064c-76.5952 0-171.8272-15.4112-229.7856-52.736a25.57952 25.57952 0 0 1-7.6288-35.3792 25.57952 25.57952 0 0 1 35.3792-7.6288c54.016 34.816 160.256 48.4352 232.7552 43.5712 14.0288-0.8704 26.3168 9.728 27.2384 23.8592 0.9216 14.08-9.728 26.3168-23.8592 27.2384-10.7008 0.7168-22.1696 1.0752-34.0992 1.0752z' fill='{{(isStr ? colors : colors[1]) || 'rgb(255,51,85)'}}' /%3E%3Cpath d='M512 699.8016c-243.2 0-426.5984-81.2032-426.5984-188.928 0-25.6512 10.24-50.1248 30.5152-72.8064a25.6 25.6 0 1 1 38.1952 34.0992c-11.6224 13.0048-17.5104 26.0608-17.5104 38.7584 0 65.1264 154.1632 137.728 375.3984 137.728s375.3984-72.5504 375.3984-137.728c0-13.568-6.7072-27.4944-19.9168-41.3696-9.728-10.24-9.3696-26.4704 0.8704-36.1984 10.24-9.7792 26.4704-9.3696 36.1984 0.8704 22.5792 23.7056 34.048 49.5104 34.048 76.6976 0 107.6736-183.3984 188.8768-426.5984 188.8768z' fill='{{(isStr ? colors : colors[2]) || 'rgb(35,24,21)'}}' /%3E%3Cpath d='M512 929.6384c-243.2 0-426.5984-81.2032-426.5984-188.928 0-25.6512 10.24-50.1248 30.5152-72.8064a25.6 25.6 0 1 1 38.1952 34.0992c-11.6224 13.0048-17.5104 26.0608-17.5104 38.7584 0 65.1264 154.1632 137.728 375.3984 137.728s375.3984-72.5504 375.3984-137.728c0-13.568-6.7072-27.4944-19.9168-41.3696-9.728-10.24-9.3696-26.4704 0.8704-36.1984 10.24-9.7792 26.4704-9.3696 36.1984 0.8704 22.5792 23.7056 34.048 49.5104 34.048 76.6976 0 107.6736-183.3984 188.8768-426.5984 188.8768z' fill='{{(isStr ? colors : colors[3]) || 'rgb(35,24,21)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--wangdian-->
<view tt:if="{{name === 'wangdian'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M506.1632 796.3136c-4.4032 0-8.8064-1.1264-12.7488-3.3792-143.104-82.0224-288-246.2208-288-401.3056 0-165.8368 134.912-300.7488 300.7488-300.7488S806.912 225.792 806.912 391.6288c0 172.4416-176.6912 337.664-288.0512 401.3056-3.9424 2.2528-8.3456 3.3792-12.6976 3.3792z m0-654.2336c-137.5744 0-249.5488 111.9232-249.5488 249.5488 0 129.6896 126.2592 274.0224 249.4976 349.3376 107.7248-66.304 249.5488-212.0704 249.5488-349.3376 0.0512-137.5744-111.9232-249.5488-249.4976-249.5488z' fill='{{(isStr ? colors : colors[0]) || 'rgb(35,24,21)'}}' /%3E%3Cpath d='M506.1632 542.6688c-83.3024 0-151.04-67.7888-151.04-151.04s67.7888-151.04 151.04-151.04 151.04 67.7888 151.04 151.04-67.7376 151.04-151.04 151.04z m0-250.88c-55.04 0-99.84 44.8-99.84 99.84s44.8 99.84 99.84 99.84 99.84-44.8 99.84-99.84-44.8-99.84-99.84-99.84z' fill='{{(isStr ? colors : colors[1]) || 'rgb(255,51,85)'}}' /%3E%3Cpath d='M871.4752 934.4512H150.2208c-19.2512 0-36.4544-10.0352-45.9776-26.7776s-9.2672-36.6592 0.6656-53.1968l88.9856-148.3264a25.58464 25.58464 0 0 1 35.1232-8.7552 25.53856 25.53856 0 0 1 8.7552 35.1232l-88.9856 148.3264c-0.2048 0.3584-0.4608 0.8192 0 1.6384s1.024 0.8192 1.3824 0.8192h721.2032c0.4096 0 0.9728 0 1.4336-0.8704 0.4608-0.8704 0.1536-1.28-0.0512-1.6384l-96.5632-147.4048c-7.7312-11.8272-4.4544-27.6992 7.3728-35.4304 11.8272-7.7312 27.6992-4.4544 35.4304 7.3728l96.5632 147.4048c10.6496 16.2304 11.52 36.9664 2.304 54.0672s-26.9824 27.648-46.3872 27.648z' fill='{{(isStr ? colors : colors[2]) || 'rgb(35,24,21)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--piaowu-->
<view tt:if="{{name === 'piaowu'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M180.0192 924.7232a25.59488 25.59488 0 0 1-25.6-25.6V201.6768c0-54.8352 43.0592-99.4816 96-99.4816h519.424c52.9408 0 96 44.5952 96 99.4816V899.072c0 10.1888-6.0416 19.4048-15.36 23.4496s-20.1728 2.2016-27.648-4.7104l-100.5568-93.2864-96 92.928a25.58976 25.58976 0 0 1-37.12-1.6384l-76.288-87.9616-79.5648 88.32c-4.5568 5.0688-10.9568 8.0896-17.7664 8.448-6.8608 0.3072-13.4656-2.048-18.4832-6.656l-102.4512-93.5936-96.6656 93.1328c-5.0688 4.7104-11.4688 7.2192-17.92 7.2192z m113.9712-161.024c6.1952 0 12.3904 2.2528 17.2544 6.7072l101.1712 92.416 81.7152-90.6752c4.9152-5.4272 12.0832-8.3456 19.2512-8.448a25.6 25.6 0 0 1 19.0976 8.8064l77.568 89.3952 94.0032-90.9824a25.58464 25.58464 0 0 1 35.2256-0.3584l75.3152 69.888V201.6768c0-26.624-20.0704-48.2816-44.8-48.2816H250.368c-24.6784 0-44.8 21.6576-44.8 48.2816v637.184l70.6048-68.0448c4.9664-4.7104 11.3664-7.1168 17.8176-7.1168z' fill='{{(isStr ? colors : colors[0]) || 'rgb(35,24,21)'}}' /%3E%3Cpath d='M655.5136 512.768H535.04V433.7152h120.5248c14.1312 0 25.6-11.4688 25.6-25.6s-11.4688-25.6-25.6-25.6h-98.4064l76.544-70.8608a25.58976 25.58976 0 0 0 1.3824-36.1984 25.58976 25.58976 0 0 0-36.1984-1.3824L508.5696 357.6832l-88.4224-83.456c-10.2912-9.728-26.4704-9.216-36.1984 1.024-9.728 10.2912-9.216 26.4704 1.024 36.1984l75.264 71.0656H363.3152c-14.1312 0-25.6 11.4688-25.6 25.6s11.4688 25.6 25.6 25.6H483.84v79.0528H363.3152c-14.1312 0-25.6 11.4688-25.6 25.6s11.4688 25.6 25.6 25.6H483.84v83.2c0 14.1312 11.4688 25.6 25.6 25.6s25.6-11.4688 25.6-25.6v-83.2h120.5248c14.1312 0 25.6-11.4688 25.6-25.6s-11.4688-25.6-25.6512-25.6z' fill='{{(isStr ? colors : colors[1]) || 'rgb(255,51,85)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--licaichanpin2-->
<view tt:if="{{name === 'licaichanpin2'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M503.552 499.456c-9.3184 0-18.5856-2.2016-27.0848-6.5536L182.016 340.736c-18.688-9.6256-30.3104-28.3648-30.3616-48.8448-0.0512-20.48 11.52-39.2704 30.1568-48.9984l301.2096-157.184c16.9472-8.8576 37.2224-8.9088 54.2208-0.1536l301.0048 154.8288c18.8928 9.728 30.5664 28.6208 30.464 49.3056-0.1024 20.6848-11.9808 39.4752-30.976 48.9984l-307.7632 154.5216c-8.2944 4.1984-17.3568 6.2464-26.4192 6.2464zM205.5168 288.3072c-1.7408 0.9216-2.6112 2.048-2.6112 3.4816s0.8704 2.56 2.6624 3.4816l294.4512 152.1664c2.1504 1.1264 4.9152 1.1264 7.0144 0.0512l307.7632-154.5216c1.8432-0.9216 2.7136-2.048 2.7136-3.5328 0-1.4336-0.8704-2.56-2.6624-3.5328L513.8432 131.072c-2.1504-1.1264-4.9664-1.0752-7.1168 0L205.5168 288.3072zM415.488 942.6432c-9.8304 0-19.6608-2.4576-28.4672-7.3216l-275.5072-152.3712c-17.8176-9.8304-28.8768-28.3136-28.8768-48.128l-0.3072-286.5664c0-19.2512 10.0352-36.864 26.88-47.104 17.7664-10.752 39.424-11.4688 57.8048-1.792l275.8144 144.1792c18.5856 9.728 30.1056 28.416 30.1056 48.8448v294.7072c0 19.5072-10.2912 37.2224-27.4944 47.4112-9.216 5.4272-19.5584 8.1408-29.952 8.1408zM139.7248 443.8528c-1.9456 0-3.4304 0.768-3.9936 1.1264-2.2016 1.3312-2.2016 2.7648-2.2016 3.2768l0.3072 286.5664c0 1.28 0.9728 2.56 2.4576 3.3792l275.5072 152.3712c3.584 1.9968 6.7584 0.4096 7.6288-0.1024 2.304-1.3312 2.304-2.816 2.304-3.2768V592.384c0-1.28-1.024-2.6112-2.6112-3.4816L143.2576 444.7232c-1.28-0.6656-2.4576-0.8704-3.5328-0.8704z' fill='{{(isStr ? colors : colors[0]) || 'rgb(35,24,21)'}}' /%3E%3Cpath d='M601.4464 942.6432c-10.3936 0-20.736-2.7136-29.952-8.1408-17.2032-10.1376-27.4944-27.8528-27.4944-47.4112V592.384c0-20.4288 11.52-39.1168 30.1056-48.8448L849.92 399.36c18.432-9.6256 40.0384-8.96 57.8048 1.792 16.8448 10.24 26.88 27.8016 26.88 47.104l-0.3072 286.5664c0 19.8656-11.1104 38.2976-28.8768 48.128l-275.5072 152.3712c-8.8064 4.864-18.6368 7.3216-28.4672 7.3216z m275.7632-498.7904c-1.0752 0-2.304 0.256-3.5328 0.8704l-275.8144 144.1792c-1.5872 0.8704-2.6112 2.2016-2.6112 3.4816v294.7072c0 0.4608 0 1.9456 2.304 3.2768 0.8704 0.512 4.0448 2.0992 7.6288 0.1024l275.456-152.32c1.536-0.8192 2.4576-2.1504 2.4576-3.3792l0.3072-286.5664c0-0.4608 0-1.8944-2.2016-3.2768-0.5632-0.3072-2.048-1.0752-3.9936-1.0752z m-15.4112-21.8112z' fill='{{(isStr ? colors : colors[1]) || 'rgb(255,51,85)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--licaichanpin-->
<view tt:if="{{name === 'licaichanpin'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M509.6448 537.2928c-15.1552 0-30.3104-3.2256-44.544-9.7792L144.1792 381.0304c-16.128-7.3728-26.624-22.9376-27.392-40.6016s8.2944-34.0992 23.6544-42.9056L461.312 114.944a107.06944 107.06944 0 0 1 101.3248-2.56l318.9248 160.9216c15.7696 7.9872 25.6512 23.9104 25.8048 41.5744s-9.5744 33.7408-25.2416 41.9328l-322.8672 168.2432c-15.6672 8.192-32.6144 12.2368-49.6128 12.2368zM173.0048 337.92L486.4 480.9728c15.8208 7.2192 33.7408 6.7584 49.152-1.28l315.4944-164.4032-311.5008-157.1328a55.79264 55.79264 0 0 0-52.9408 1.3312L173.0048 337.92z m685.4656-18.8928h0.0512-0.0512z' fill='{{(isStr ? colors : colors[0]) || 'rgb(35,24,21)'}}' /%3E%3Cpath d='M508.9792 719.2576c-14.7456 0-29.44-3.1232-43.3152-9.4208l-349.8496-158.464c-12.9024-5.8368-18.5856-20.992-12.7488-33.8944s20.992-18.5856 33.8944-12.7488l349.8496 158.464c15.2064 6.912 32.5632 6.3488 47.5136-1.4848l352.2048-184.6272a25.6 25.6 0 0 1 34.56 10.8032c6.5536 12.544 1.7408 28.0064-10.8032 34.56L558.08 707.072c-15.5136 8.0896-32.3072 12.1856-49.1008 12.1856z' fill='{{(isStr ? colors : colors[1]) || 'rgb(35,24,21)'}}' /%3E%3Cpath d='M508.2112 900.3008c-14.7456 0-29.44-3.1232-43.3152-9.4208l-349.8496-158.464a25.57952 25.57952 0 0 1-12.7488-33.8944 25.57952 25.57952 0 0 1 33.8944-12.7488l349.8496 158.464c15.2064 6.912 32.512 6.3488 47.4624-1.4848l352.2048-184.6272a25.6 25.6 0 0 1 34.56 10.8032c6.5536 12.544 1.7408 28.0064-10.8032 34.56l-352.2048 184.6272c-15.4624 8.0896-32.256 12.1856-49.0496 12.1856z' fill='{{(isStr ? colors : colors[2]) || 'rgb(35,24,21)'}}' /%3E%3Cpath d='M356.1984 344.6784c-8.9088 0-17.5616-4.6592-22.272-12.9024a25.58976 25.58976 0 0 1 9.5744-34.9184l137.8816-78.592c12.288-7.0144 27.904-2.7136 34.9184 9.5744 7.0144 12.288 2.7136 27.904-9.5744 34.9184L368.8448 341.2992c-3.9936 2.304-8.3456 3.3792-12.6464 3.3792z' fill='{{(isStr ? colors : colors[3]) || 'rgb(255,51,85)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--heart-fill-->
<view tt:if="{{name === 'heart-fill'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M923 283.6c-13.4-31.1-32.6-58.9-56.9-82.8-24.3-23.8-52.5-42.4-84-55.5-32.5-13.5-66.9-20.3-102.4-20.3-49.3 0-97.4 13.5-139.2 39-10 6.1-19.5 12.8-28.5 20.1-9-7.3-18.5-14-28.5-20.1-41.8-25.5-89.9-39-139.2-39-35.5 0-69.9 6.8-102.4 20.3-31.4 13-59.7 31.7-84 55.5-24.4 23.9-43.5 51.7-56.9 82.8-13.9 32.3-21 66.6-21 101.9 0 33.3 6.8 68 20.3 103.3 11.3 29.5 27.5 60.1 48.2 91 32.8 48.9 77.9 99.9 133.9 151.6 92.8 85.7 184.7 144.9 188.6 147.3l23.7 15.2c10.5 6.7 24 6.7 34.5 0l23.7-15.2c3.9-2.5 95.7-61.6 188.6-147.3 56-51.7 101.1-102.7 133.9-151.6 20.7-30.9 37-61.5 48.2-91 13.5-35.3 20.3-70 20.3-103.3 0.1-35.3-7-69.6-20.9-101.9z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--close-circle-fill-->
<view tt:if="{{name === 'close-circle-fill'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m165.4 618.2l-66-0.3L512 563.4l-99.3 118.4-66.1 0.3c-4.4 0-8-3.5-8-8 0-1.9 0.7-3.7 1.9-5.2l130.1-155L340.5 359c-1.2-1.5-1.9-3.3-1.9-5.2 0-4.4 3.6-8 8-8l66.1 0.3L512 464.6l99.3-118.4 66-0.3c4.4 0 8 3.5 8 8 0 1.9-0.7 3.7-1.9 5.2L553.5 514l130 155c1.2 1.5 1.9 3.3 1.9 5.2 0 4.4-3.6 8-8 8z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--search-->
<view tt:if="{{name === 'search'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6c3.2 3.2 8.4 3.2 11.6 0l43.6-43.5c3.2-3.2 3.2-8.4 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />

<!--right-->
<view tt:if="{{name === 'right'}}" style="background-image: url({{quot}}data:image/svg+xml, %3Csvg viewBox='0 0 1024 1024' xmlns='http://www.w3.org/2000/svg' width='{{svgSize}}px' height='{{svgSize}}px'%3E%3Cpath d='M765.7 486.8L314.9 134.7c-5.3-4.1-12.9-0.4-12.9 6.3v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1c16.4-12.8 16.4-37.6 0-50.4z' fill='{{(isStr ? colors : colors[0]) || 'rgb(51,51,51)'}}' /%3E%3C/svg%3E{{quot}}); width: {{svgSize}}px; height: {{svgSize}}px; " class="icon" />