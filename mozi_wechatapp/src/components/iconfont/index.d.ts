/* eslint-disable */
import React, { FunctionComponent } from 'react';

interface Props {
  name: 'check' | 'shequ' | 'message' | 'ellipsis' | 'bell-fill' | 'arrawsalt' | 'close' | 'info-circle' | 'plus-square' | 'moneycollect' | 'attachment' | 'share' | 'wechat-fill' | 'm-pingfen' | 'info-circle-fill' | 'file-copy' | 'caret-down' | 'caret-up' | 'bodongfenxi' | 'jijin' | 'jiaoyichaxun' | 'jifen' | 'wangdian' | 'piaowu' | 'licaichanpin2' | 'licaichanpin' | 'heart-fill' | 'close-circle-fill' | 'search' | 'right';
  size?: number;
  color?: string | string[];
  style?: React.CSSProperties;
}

declare const IconFont: FunctionComponent<Props>;

export default IconFont;
