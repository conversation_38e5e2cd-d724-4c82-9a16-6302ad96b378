/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconWangdian = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M506.1632 796.3136c-4.4032 0-8.8064-1.1264-12.7488-3.3792-143.104-82.0224-288-246.2208-288-401.3056 0-165.8368 134.912-300.7488 300.7488-300.7488S806.912 225.792 806.912 391.6288c0 172.4416-176.6912 337.664-288.0512 401.3056-3.9424 2.2528-8.3456 3.3792-12.6976 3.3792z m0-654.2336c-137.5744 0-249.5488 111.9232-249.5488 249.5488 0 129.6896 126.2592 274.0224 249.4976 349.3376 107.7248-66.304 249.5488-212.0704 249.5488-349.3376 0.0512-137.5744-111.9232-249.5488-249.4976-249.5488z"
        fill={getIconColor(color, 0, '#231815')}
      />
      <path
        d="M506.1632 542.6688c-83.3024 0-151.04-67.7888-151.04-151.04s67.7888-151.04 151.04-151.04 151.04 67.7888 151.04 151.04-67.7376 151.04-151.04 151.04z m0-250.88c-55.04 0-99.84 44.8-99.84 99.84s44.8 99.84 99.84 99.84 99.84-44.8 99.84-99.84-44.8-99.84-99.84-99.84z"
        fill={getIconColor(color, 1, '#FF3355')}
      />
      <path
        d="M871.4752 934.4512H150.2208c-19.2512 0-36.4544-10.0352-45.9776-26.7776s-9.2672-36.6592 0.6656-53.1968l88.9856-148.3264a25.58464 25.58464 0 0 1 35.1232-8.7552 25.53856 25.53856 0 0 1 8.7552 35.1232l-88.9856 148.3264c-0.2048 0.3584-0.4608 0.8192 0 1.6384s1.024 0.8192 1.3824 0.8192h721.2032c0.4096 0 0.9728 0 1.4336-0.8704 0.4608-0.8704 0.1536-1.28-0.0512-1.6384l-96.5632-147.4048c-7.7312-11.8272-4.4544-27.6992 7.3728-35.4304 11.8272-7.7312 27.6992-4.4544 35.4304 7.3728l96.5632 147.4048c10.6496 16.2304 11.52 36.9664 2.304 54.0672s-26.9824 27.648-46.3872 27.648z"
        fill={getIconColor(color, 2, '#231815')}
      />
    </svg>
  );
};

IconWangdian.defaultProps = {
  size: 24,
};

export default IconWangdian;
