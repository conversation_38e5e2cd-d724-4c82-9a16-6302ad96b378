/* eslint-disable */

import React from 'react';
import IconCheck from './IconCheck';
import IconShequ from './IconShequ';
import IconMessage from './IconMessage';
import IconEllipsis from './IconEllipsis';
import IconBellFill from './IconBellFill';
import IconArrawsalt from './IconArrawsalt';
import IconClose from './IconClose';
import IconInfoCircle from './IconInfoCircle';
import IconPlusSquare from './IconPlusSquare';
import IconMoneycollect from './IconMoneycollect';
import IconAttachment from './IconAttachment';
import IconShare from './IconShare';
import IconWechatFill from './IconWechatFill';
import IconMPingfen from './IconMPingfen';
import IconInfoCircleFill from './IconInfoCircleFill';
import IconFileCopy from './IconFileCopy';
import IconCaretDown from './IconCaretDown';
import IconCaretUp from './IconCaretUp';
import IconBodongfenxi from './IconBodongfenxi';
import IconJijin from './IconJijin';
import IconJiaoyichaxun from './IconJiaoyichaxun';
import IconJifen from './IconJifen';
import IconWangdian from './IconWangdian';
import IconPiaowu from './IconPiaowu';
import IconLicaichanpin2 from './IconLicaichanpin2';
import IconLicaichanpin from './IconLicaichanpin';
import IconHeartFill from './IconHeartFill';
import IconCloseCircleFill from './IconCloseCircleFill';
import IconSearch from './IconSearch';
import IconRight from './IconRight';
export { default as IconCheck } from './IconCheck';
export { default as IconShequ } from './IconShequ';
export { default as IconMessage } from './IconMessage';
export { default as IconEllipsis } from './IconEllipsis';
export { default as IconBellFill } from './IconBellFill';
export { default as IconArrawsalt } from './IconArrawsalt';
export { default as IconClose } from './IconClose';
export { default as IconInfoCircle } from './IconInfoCircle';
export { default as IconPlusSquare } from './IconPlusSquare';
export { default as IconMoneycollect } from './IconMoneycollect';
export { default as IconAttachment } from './IconAttachment';
export { default as IconShare } from './IconShare';
export { default as IconWechatFill } from './IconWechatFill';
export { default as IconMPingfen } from './IconMPingfen';
export { default as IconInfoCircleFill } from './IconInfoCircleFill';
export { default as IconFileCopy } from './IconFileCopy';
export { default as IconCaretDown } from './IconCaretDown';
export { default as IconCaretUp } from './IconCaretUp';
export { default as IconBodongfenxi } from './IconBodongfenxi';
export { default as IconJijin } from './IconJijin';
export { default as IconJiaoyichaxun } from './IconJiaoyichaxun';
export { default as IconJifen } from './IconJifen';
export { default as IconWangdian } from './IconWangdian';
export { default as IconPiaowu } from './IconPiaowu';
export { default as IconLicaichanpin2 } from './IconLicaichanpin2';
export { default as IconLicaichanpin } from './IconLicaichanpin';
export { default as IconHeartFill } from './IconHeartFill';
export { default as IconCloseCircleFill } from './IconCloseCircleFill';
export { default as IconSearch } from './IconSearch';
export { default as IconRight } from './IconRight';

const IconFont = ({ name, ...rest }) => {
  switch (name) {
    case 'check':
      return <IconCheck {...rest} />;
    case 'shequ':
      return <IconShequ {...rest} />;
    case 'message':
      return <IconMessage {...rest} />;
    case 'ellipsis':
      return <IconEllipsis {...rest} />;
    case 'bell-fill':
      return <IconBellFill {...rest} />;
    case 'arrawsalt':
      return <IconArrawsalt {...rest} />;
    case 'close':
      return <IconClose {...rest} />;
    case 'info-circle':
      return <IconInfoCircle {...rest} />;
    case 'plus-square':
      return <IconPlusSquare {...rest} />;
    case 'moneycollect':
      return <IconMoneycollect {...rest} />;
    case 'attachment':
      return <IconAttachment {...rest} />;
    case 'share':
      return <IconShare {...rest} />;
    case 'wechat-fill':
      return <IconWechatFill {...rest} />;
    case 'm-pingfen':
      return <IconMPingfen {...rest} />;
    case 'info-circle-fill':
      return <IconInfoCircleFill {...rest} />;
    case 'file-copy':
      return <IconFileCopy {...rest} />;
    case 'caret-down':
      return <IconCaretDown {...rest} />;
    case 'caret-up':
      return <IconCaretUp {...rest} />;
    case 'bodongfenxi':
      return <IconBodongfenxi {...rest} />;
    case 'jijin':
      return <IconJijin {...rest} />;
    case 'jiaoyichaxun':
      return <IconJiaoyichaxun {...rest} />;
    case 'jifen':
      return <IconJifen {...rest} />;
    case 'wangdian':
      return <IconWangdian {...rest} />;
    case 'piaowu':
      return <IconPiaowu {...rest} />;
    case 'licaichanpin2':
      return <IconLicaichanpin2 {...rest} />;
    case 'licaichanpin':
      return <IconLicaichanpin {...rest} />;
    case 'heart-fill':
      return <IconHeartFill {...rest} />;
    case 'close-circle-fill':
      return <IconCloseCircleFill {...rest} />;
    case 'search':
      return <IconSearch {...rest} />;
    case 'right':
      return <IconRight {...rest} />;

  }

  return null;
};

export default IconFont;
