/* eslint-disable */

import React from 'react';
import { getIconColor } from './helper';

const DEFAULT_STYLE = {
  display: 'block',
};

const IconLicaichanpin = ({ size, color, style: _style, ...rest }) => {
  const style = _style ? { ...DEFAULT_STYLE, ..._style } : DEFAULT_STYLE;

  return (
    <svg viewBox="0 0 1024 1024" width={size + 'rem'} height={size + 'rem'} style={style} {...rest}>
      <path
        d="M509.6448 537.2928c-15.1552 0-30.3104-3.2256-44.544-9.7792L144.1792 381.0304c-16.128-7.3728-26.624-22.9376-27.392-40.6016s8.2944-34.0992 23.6544-42.9056L461.312 114.944a107.06944 107.06944 0 0 1 101.3248-2.56l318.9248 160.9216c15.7696 7.9872 25.6512 23.9104 25.8048 41.5744s-9.5744 33.7408-25.2416 41.9328l-322.8672 168.2432c-15.6672 8.192-32.6144 12.2368-49.6128 12.2368zM173.0048 337.92L486.4 480.9728c15.8208 7.2192 33.7408 6.7584 49.152-1.28l315.4944-164.4032-311.5008-157.1328a55.79264 55.79264 0 0 0-52.9408 1.3312L173.0048 337.92z m685.4656-18.8928h0.0512-0.0512z"
        fill={getIconColor(color, 0, '#231815')}
      />
      <path
        d="M508.9792 719.2576c-14.7456 0-29.44-3.1232-43.3152-9.4208l-349.8496-158.464c-12.9024-5.8368-18.5856-20.992-12.7488-33.8944s20.992-18.5856 33.8944-12.7488l349.8496 158.464c15.2064 6.912 32.5632 6.3488 47.5136-1.4848l352.2048-184.6272a25.6 25.6 0 0 1 34.56 10.8032c6.5536 12.544 1.7408 28.0064-10.8032 34.56L558.08 707.072c-15.5136 8.0896-32.3072 12.1856-49.1008 12.1856z"
        fill={getIconColor(color, 1, '#231815')}
      />
      <path
        d="M508.2112 900.3008c-14.7456 0-29.44-3.1232-43.3152-9.4208l-349.8496-158.464a25.57952 25.57952 0 0 1-12.7488-33.8944 25.57952 25.57952 0 0 1 33.8944-12.7488l349.8496 158.464c15.2064 6.912 32.512 6.3488 47.4624-1.4848l352.2048-184.6272a25.6 25.6 0 0 1 34.56 10.8032c6.5536 12.544 1.7408 28.0064-10.8032 34.56l-352.2048 184.6272c-15.4624 8.0896-32.256 12.1856-49.0496 12.1856z"
        fill={getIconColor(color, 2, '#231815')}
      />
      <path
        d="M356.1984 344.6784c-8.9088 0-17.5616-4.6592-22.272-12.9024a25.58976 25.58976 0 0 1 9.5744-34.9184l137.8816-78.592c12.288-7.0144 27.904-2.7136 34.9184 9.5744 7.0144 12.288 2.7136 27.904-9.5744 34.9184L368.8448 341.2992c-3.9936 2.304-8.3456 3.3792-12.6464 3.3792z"
        fill={getIconColor(color, 3, '#FF3355')}
      />
    </svg>
  );
};

IconLicaichanpin.defaultProps = {
  size: 24,
};

export default IconLicaichanpin;
