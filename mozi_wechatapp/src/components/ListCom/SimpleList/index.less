.scroll-list {
  .header {
    display: flex;
    height: 22vh;
    box-sizing: border-box;
    background-color: #f5f5f5;

    .left {
      flex: 1;
      padding: 20rpx 0 20rpx 60rpx;

      .title {
        font-weight: bold;
        font-size: 36px;
        margin-bottom: 20px;
      }

      .desc {
        display: flex;
        margin-top: 20px;
        font-size: 24px;

        .desc-con {
          margin-right: 10px;
        }

        .picker-select {
          display: flex;
          align-items: center;

          .select-icon {
            margin-right: 10px;
          }
        }
      }
    }

    .right {
      flex: 1;
      width: 50%;
      display: flex;
      justify-content: center;
      align-items: center;

      .header-img {
        height: 18vh;
      }
    }
  }

  .gridTitle {
    font-size: 20px;
    color: #898989;
    // padding-left: 24px;
    margin-bottom: 10px;
    position: fixed;
    top: 0;
    left: 0;
    background-color: #dfdfdf;
    z-index: 10;
    align-items: center;
    padding: 10px 20px;
    box-sizing: border-box;

    .text {
      text-align: right;
    }
  }

  .show-header-grid {
    top: 22vh;
  }

  .gridListItem {
    padding: 0 20px;
  }
}

.scroll {
  height: 100vh;
  // padding-top: 60rpx;
  box-sizing: border-box;
  position: relative;
  background-color: #fff;
  padding-bottom: 20px;


  

  .gridText {
    display: flex;
    width: 100%;

    .gridIcon {
      height: 36px;
      width: 36px;
      margin-right: 10px;
    }
  }

  .list {
    margin-top: 50px;

    .gridListItem {
      padding: 0 20px;
    }
  }
}

.show-header {
  height: 78vh;
}