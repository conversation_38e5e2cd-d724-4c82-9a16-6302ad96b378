page {
  font-size: 28px;
}

.adm-list-item-content {
  padding-right: 0 !important;
}


.gridTitle {
  width: 100%;
}

.girdIcon {
  width: 20px;
  height: 20px;
  margin-right: 4px;
}

// 列表title
.rankTitle {
  display: flex;
  width: 100%;


  .rankImg {
    height: 60px;
    width: 60px;
    margin-right: 20px;
  }

  .rankCoin {
    font-weight: bold;
  }

  .rankCoinDesc {
    color: #555e68;
  }
}

// 列表desc
.rankDesc {
  text-align: right;

  .rankPrice {
    font-weight: bold;
  }

  .rankPriceChange {
    color: #555e68;
  }

  .rankRed {
    color: #ff3333;
  }

  .rankGreen {
    color: #02c076;
  }
}

.login-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 26px;

  .login-btn {
    margin-top: 40px;
    background-color: #02c076;
    color: #fff;
  }
}