// 首页mock
// 热门交易所
export const mock_hotjiaoyisuo = {
  "code": 0,
  "errorMsg": null,
  "data": [{
      "exchange": "binance",
      "totalSpot": "18949913439.45",
      "totalFuture": "42688895568.00",
      "dt": "2024-06-03"
    },
    {
      "exchange": "okx",
      "totalSpot": "2417757511.10",
      "totalFuture": "19552230349.74",
      "dt": "2024-06-03"
    },
    {
      "exchange": "htx",
      "totalSpot": "2597648035.16",
      "totalFuture": "1808007731.31",
      "dt": "2024-06-03"
    },
    {
      "exchange": "coinbase",
      "totalSpot": "1929488896.31",
      "totalFuture": "515513949.55",
      "dt": "2024-06-03"
    },
    {
      "exchange": "kucoin",
      "totalSpot": "790728901.81",
      "totalFuture": "1718751490.62",
      "dt": "2024-06-03"
    },
    {
      "exchange": "bitfinex",
      "totalSpot": "147579910.62",
      "totalFuture": "92817357.08",
      "dt": "2024-06-03"
    },
    {
      "exchange": "kraken",
      "totalSpot": "631884677.23",
      "totalFuture": "*********.68",
      "dt": "2024-06-03"
    },
    {
      "exchange": "gate",
      "totalSpot": "**********.44",
      "totalFuture": "**********.00",
      "dt": "2024-06-03"
    },
    {
      "exchange": "bitget",
      "totalSpot": "**********.17",
      "totalFuture": "***********.13",
      "dt": "2024-06-03"
    },
    {
      "exchange": "upbit",
      "totalSpot": "**********.97",
      "totalFuture": "0.00",
      "dt": "2024-06-03"
    }
  ]
};

// 热门版块
export const mock_hotbankuai = {
  "code": 0,
  "errorMsg": null,
  "data": [
    
      {
          "section": "CEX",
          "changes": "1.66",
          "dt": "2024-06-03"
      },
      {
        "section": "Gaming",
        "changes": "6.22",
        "dt": "2024-06-03"
      },
      {
          "section": "NFT",
          "changes": "10.28",
          "dt": "2024-06-03"
      },
      {
          "section": "Hong Kong Zone",
          "changes": "-0.56",
          "dt": "2024-06-03"
      },
      {
          "section": "Tron Ecosystem",
          "changes": "-0.99",
          "dt": "2024-06-03"
      },
      {
          "section": "Dominica",
          "changes": "-0.99",
          "dt": "2024-06-03"
      },
      {
          "section": "Metaverse",
          "changes": "6.07",
          "dt": "2024-06-03"
      },
      {
          "section": "Fantom",
          "changes": "-5.42",
          "dt": "2024-06-03"
      },
      {
          "section": "AI",
          "changes": "-1.65",
          "dt": "2024-06-03"
      },
      {
          "section": "Cross-chain Bridge",
          "changes": "-0.86",
          "dt": "2024-06-03"
      }
  ]
};

// 热门合约
export const mock_hotheyue = {
  "code": 0,
  "errorMsg": null,
  "data": [
      {
          "coin": "OXT",
          "hot": "18906",
          "priceChangePercent": "-1.00",
          "dt": "2024-06-03"
      },
      {
          "coin": "RGT",
          "hot": "0",
          "priceChangePercent": "0.00",
          "dt": "2024-06-03"
      },
      {
          "coin": "BETA",
          "hot": "40247",
          "priceChangePercent": "-2.56",
          "dt": "2024-06-03"
      },
      {
          "coin": "SRM",
          "hot": "0",
          "priceChangePercent": "0.00",
          "dt": "2024-06-03"
      },
      {
          "coin": "EPX",
          "hot": "27591",
          "priceChangePercent": "-0.05",
          "dt": "2024-06-03"
      },
      {
          "coin": "TCT",
          "hot": "0",
          "priceChangePercent": "0.00",
          "dt": "2024-06-03"
      },
      {
          "coin": "MLN",
          "hot": "28500",
          "priceChangePercent": "1.10",
          "dt": "2024-06-03"
      },
      {
          "coin": "TVK",
          "hot": "0",
          "priceChangePercent": "0.00",
          "dt": "2024-06-03"
      },
      {
          "coin": "API3",
          "hot": "112515",
          "priceChangePercent": "7.24",
          "dt": "2024-06-03"
      },
      {
          "coin": "UST",
          "hot": "0",
          "priceChangePercent": "0.00",
          "dt": "2024-06-03"
      }
  ]
}
