.indexBox {
  background-color: #efefef;
  // background: linear-gradient(to bottom, #000 40%, #fff);
  // background-image: url('../../assets/mozi_logo.jpg');
  // background-size: contain;
  // background-repeat: no-repeat;
  padding: 20px;
  box-sizing: border-box;
  min-height: 100vh;

  .no-search-box {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .logo {
    width: 100%;
    height: auto;
    display: block;
  }

  .header {    
    width: 100%;
    margin-bottom: 20px;

  }

  .area-flex {
    display: flex;
    // justify-content: space-between;

    .area-flex-item {
      flex-basis: 23%;
      margin-right: 1%;
    }
  }

  .derivativeHead {
    padding: 10px;

    .title {
      display: flex;
      align-items: center;

      .titleNum {
        margin-left: 5px;
        color: #898989;
        font-size: 24px;
      }
    }
  }

  .derivativeBody {
    // display: flex;
    margin-bottom: 20px;
    // flex-wrap: wrap;

    .ownItem {
      padding-left: 24px;
      
      .adm-list-item-content {
        padding-right: 0;
      }

      .ownItemCon {
        display: flex;
        align-items: center;
      }
    }
  }

  .ownSelect {
    font-size: 20px;
    color: #898989;
    // text-align: center;
    padding-left: 24px;
    margin-bottom: 10px;
  }

  .ownItemSelect {
    font-size: 24px;
    .ownItemCon {
      display: flex;
      align-items: center;
    }
  }

  .ownList {
    // text-align: center;

    .ownItem {
      padding-left: 24px;
      
      .adm-list-item-content {
        padding-right: 0;
      }

      .ownItemCon {
        display: flex;
        align-items: center;
      }
    }
  }
}

.gridText {
  display: flex;
  width: 100%;
  

  .gridIcon {
    height: 36px;
    width: 36px;
    margin-right: 10px;
  }

  .gridName {
    flex: 1;
    white-space: nowrap;  /* 不换行 */
    overflow: hidden;     /* 超出宽度隐藏 */
    text-overflow: ellipsis;  /* 超出宽度点点显示 */
  }
}
