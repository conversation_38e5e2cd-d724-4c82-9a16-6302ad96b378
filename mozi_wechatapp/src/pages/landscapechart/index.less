.chart-box {
  width: 100vw;
  height: 100vh;
  // padding: 0 20px;
  box-sizing: border-box;
  position: relative;

  .chartTab {
    width: 80%;
    height: 25px;
    height: 6vh;
    padding: 0 20px;
    // margin-bottom: 20px;
    font-size: 12px;
    box-sizing: border-box;

    .adm-tab-bar-wrap {
      gap: 20px;
      height: 100%;
      min-height: 0;
  
      .adm-tab-bar-item {
        // height: 20px;
        border-radius: 5px;
        border: 1px solid transparent;
        // background-color: #f5f5f5;
        color: #333;
        padding: 10px 0;
      }
    
      .adm-tab-bar-item-active {
        background-color: rgba(69,232,127,0.2);
        color: rgba(69,232,127,1);
      }
    }
  }

  .mychart {
    height: 94vh;
  }

  .chart-close {
    position: absolute;
    right: 110px;
    top: 10px;
    z-index: 100;
  }
}