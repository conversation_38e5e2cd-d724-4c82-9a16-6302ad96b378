.indexBox {
  background-color: #efefef;
  // background: linear-gradient(to bottom, #000 40%, #fff);
  background-image: url('https://image-1317406749.cos.ap-shanghai.myqcloud.com/mozi_logo.png');
  background-size: contain;
  background-repeat: no-repeat;
  padding: 380px 20px 0;
  box-sizing: border-box;
  min-height: 100vh;
  overflow: hidden;

  .logo {
    width: 100%;
    height: auto;
    display: block;
  }

  .header {    
    width: 100%;
    margin-bottom: 20px;

    .searchBox {
      width: 100%;
      height: 80px;
      border-radius: 40px;
      display: flex;
      background-color: #fff;
      justify-content: space-between;
      align-items: center;
      padding: 0 40px;
      box-sizing: border-box;
    
      .searchIcon {
        // flex: 1;
        margin-right: 20px;
      }
    
      .searchInput {
        flex: 4;
        color: #b2b2b2;
      }
    
      .searchCancel {
        height: 100%;
        width: 80px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

  }
  
  .notice {
    margin-top: 20px;
    margin-bottom: 20px;
    color: #ff6430;
    font-size: 22px;
    

    .notice-item {
      border-radius: 20px;
      padding-top: 10px;
      padding-bottom: 10px;
      line-height: 30px;
      align-items: center;
      // height: auto;
      // padding-inline: 10rpx;
      // display: flex;
      // align-items: center;
      // box-sizing: border-box;

      // .notice-content {
      //   flex: 1;
      //   overflow: hidden;
      //   display: flex;
      //   align-items: center;
      //   width: auto;
      //   transition-timing-function: linear;
      //   white-space: nowrap;
      //   transition-duration: 10s;
      //   transform: translateX(-500px);
      // }
    }
  }

  .derivativeItem {
    // flex: 1;
    text-align: center;
    // width: 150px;
    // height: 200px;

    .derivativeIcon {
      display: flex;
      justify-content: center;
      margin-bottom: 10px;
    }
  }

  .derivativeHead {
    padding: 10px;
  }

  .derivativeBody {
    // display: flex;
    margin-bottom: 20px;
    // flex-wrap: wrap;

    
  }

  .ownTitle {
    display: flex;
    width: 100%;

    .ownImg {
      height: 36px;
      width: 36px;
      margin-right: 10px;
    }
  }

  .ownSelect {
    font-size: 24px;
    color: #898989;
    // text-align: center;
    padding-left: 24px;
  }

  .ownList {
    // text-align: center;

    .ownItem {
      padding-left: 24px;
      
      .adm-list-item-content {
        padding-right: 0;
      }

      .ownItemCon {
        display: flex;
        align-items: center;
      }
    }
  }

  .own-box {
    background-color: #fff;
    border-radius: 8px;

    
  }

  .tab-box {
    width: 100%;
    height: 50px;
    // padding: 0 20px;
    margin-bottom: 20px;
    font-size: 24px;

    .adm-tab-bar-wrap {
      gap: 20px;
      height: 100%;
      min-height: 0;
  
      .adm-tab-bar-item {
        // height: 20px;
        border-radius: 10px;
        border: 1px solid transparent;
        // background-color: #f5f5f5;
        color: #333;
        padding: 20px 0;
      }
    
      .adm-tab-bar-item-active {
        background-color: rgba(69,232,127,0.2);
        color: rgba(69,232,127,1);
      }
    }
  }

  .list-more {
    margin: 10px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
  }

  
}

.treemapBox {
  width: 80%;
  display: inline-block;
  margin-right: 20px;

  .treemapTitle {
    margin-bottom: 10px;
    font-weight: 600;;
  }
}

.last {
  margin-right: 0;
}
