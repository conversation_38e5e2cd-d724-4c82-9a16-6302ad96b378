.rankGridContainer {
  display: flex;

  .rankGridHead {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 26px;
    margin-right: 30px;

    .firstPic {
      width: 140px;
      height: 140px;
      margin-bottom: 20px;
    }
  }

  .rankGridDesc {
    flex: 2;
  
    .gridTitle {
      font-size: 20px;
      color: #898989;
      // padding-left: 24px;
      margin-bottom: 10px;
    
      .text {
        text-align: right;
      }
    }
      
    .gridListItem {
      padding-left: 0;
    }
    
    .gridContent {
      font-size: 24px;
    
      .gridConItem {
        display: flex;
        align-items: center;
      }
    
      .text {
        justify-content: flex-end;
      }
    }
  }
}




