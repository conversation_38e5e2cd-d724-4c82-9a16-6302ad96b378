.findPage {
  // padding: 0 20px;
  background-color: #efefef;
  min-height: 100vh;
  // height: 100%;

  .ownBox {
    height: 100vh;
    padding-top: 110px;

    .scroll {
      height: 100vh;
    }

    .gridTitle {
      font-size: 20px;
      color: #898989;
      // padding-left: 24px;
      margin-bottom: 10px;
      position: fixed;
      top: 60px;
      background-color: #efefef;
      z-index: 200;
      align-items: center;
      padding: 10px 20px;
      box-sizing: border-box;
    
      .text {
        text-align: right;
      }
    }
    
    .gridListItem {
      padding: 0 20px;
    }

    .ownTitle {
      display: flex;
      width: 100%;
  
      .ownImg {
        height: 36px;
        width: 36px;
        margin-right: 10px;
      }
    }

    .addOwnBtn {
      // margin-bottom: 40px;
      background-color: #02c076;
      color: #fff;
      // display: inline-block;
      width: 240px;
      margin: 40px auto;
    }
  }

  .marketBox {
    height: 100vh;
    .scroll {
      margin-top: 8vh;
    }

    .gridTitle {
      font-size: 20px;
      color: #898989;
      // padding-left: 24px;
      margin-bottom: 10px;
      position: fixed;
      top: 60px;
      background-color: #efefef;
      z-index: 200;
      align-items: center;
      padding: 10px 20px;
      box-sizing: border-box;
    
      .text {
        text-align: right;
      }
    }
    
    .gridListItem {
      padding: 0 20px;
    }
  }

  .tabContainer {
    // margin-bottom: 20px;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    z-index: 100;
    height: 60px;
    font-weight: bold;

    .adm-tab-bar-item {
      color: #000;
    }

    
    .adm-tab-bar-item-active {
      color: #45e87f;
    }


  }


  // .ownList {
  //   height: 90vh;
  //   padding-top: 110rpx;
  //   box-sizing: border-box;
  //   position: relative;
  //   background-color: #fff;

  //   .ownTitle {
  //     display: flex;
  //     width: 100%;
  
  //     .ownImg {
  //       height: 36px;
  //       width: 36px;
  //       margin-right: 10px;
  //     }
  //   }

  //   .gridTitle {
  //     position: fixed;
  //     top: 60px;
  //     background-color: #dfdfdf;
  //     z-index: 10;
  //     align-items: center;
  //     padding: 10px 0 10px 20px;
  //   }

  //   // .gridConItem {
  //   //   padding-left: 20px;
  //   // }

  //   // .list {
  //   //   margin-top: 110px;
  //   // }
  // }
}

.rank {
  padding: 80rpx 20rpx 0px;
  overflow: hidden;

  .gridText {
    display: flex;
    width: 100%;

    .gridIcon {
      height: 36px;
      width: 36px;
      margin-right: 10px;
    }
  }
}

.rankTitle {
  display: flex;
  // align-items: baseline;

  .rankTitleTime {
    font-size: 20px;
    color: #898989;
    margin-left: 10px;
  }
}

.rank-title {
  display: flex;
  align-items: baseline;

  .rank-title-time {
    font-size: 20px;
    color: #898989;
    margin-left: 10px;
  }
}

.gridText {
  display: flex;
  align-items: center;
}

