/* 话题详情页样式 */
.topic-detail {
  padding-bottom: 120px;
  background-color: #f5f5f5;
  min-height: 100vh;

  /* 话题头部 */
  .topic-header {
    background-color: #fff;
    padding: 30px;
    margin-bottom: 20px;

    .title-section {
      margin-bottom: 20px;

      .title {
        font-size: 36px;
        font-weight: bold;
        color: #333;
      }
    }

    .description {
      font-size: 28px;
      color: #666;
      margin-bottom: 30px;
      line-height: 1.5;
    }

    .stats {
      display: flex;
      font-size: 24px;
      color: #999;

      .stat-item {
        margin-right: 30px;
      }
    }
  }

  /* 帖子列表 */
  .post-list {
    .list-header {
      background-color: #fff;
      padding: 20px 30px;
      border-bottom: 1px solid #eee;

      .total {
        font-size: 28px;
        font-weight: bold;
        color: #333;
      }
    }

    /* 评论卡片样式 */
    .comment-card {
      background-color: #fff;
      padding: 30px;
      margin-bottom: 20px;
      position: relative;

      /* 编辑操作按钮 */
      .edit-actions {
        position: absolute;
        top: 30px;
        right: 30px;
        z-index: 1;
      }

      /* 用户信息 */
      .user-info {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        .avatar {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          margin-right: 20px;
        }

        .nickname {
          font-size: 28px;
          color: #333;
          font-weight: 500;
        }
      }

      /* 内容标签 */
      .content-tag {
        display: inline-block;
        font-size: 24px;
        color: #45e87f;
        background-color: rgba(69, 232, 127, 0.1);
        padding: 6px 16px;
        border-radius: 8px;
        margin-bottom: 20px;
      }

      /* 标题 */
      .title {
        display: block;
        font-size: 32px;
        font-weight: bold;
        color: #333;
        margin-bottom: 16px;
        line-height: 1.4;
      }

      /* 描述 */
      .description {
        display: block;
        font-size: 28px;
        color: #666;
        margin-bottom: 20px;
        line-height: 1.5;
        word-break: break-all;
      }

      /* 币种和话题标签容器 */
      .tags-topics-container {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 20px;

        /* 币种标签 */
        .coin-tag {
          display: inline-block;
          font-size: 24px;
          color: #ff9500;
          background-color: rgba(255, 149, 0, 0.1);
          padding: 6px 16px;
          border-radius: 8px;
          margin-right: 16px;
          margin-bottom: 16px;

          &:active {
            opacity: 0.7;
          }
        }

        /* 话题标签 */
        .topic-tag {
          display: inline-block;
          font-size: 24px;
          color: #007aff;
          background-color: rgba(0, 122, 255, 0.1);
          padding: 6px 16px;
          border-radius: 8px;
          margin-right: 16px;
          margin-bottom: 16px;

          &:active {
            opacity: 0.7;
          }
        }
      }

      /* 操作按钮 */
      .action-buttons {
        display: flex;
        justify-content: space-between;
        border-top: 1px solid #eee;
        padding-top: 20px;

        .action-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          color: #999;
          background: none;
          padding: 0;
          line-height: normal;
          border: none;
          outline: none;
          width: auto;
          min-width: 0;
          min-height: 0;
          height: auto;

          &::after {
            border: none;
          }

          &.liked {
            color: #ff4d4f;
          }
        }
      }
    }

    /* 底部提示 */
    .list-footer {
      text-align: center;
      padding: 30px 0;

      .footer-text {
        font-size: 24px;
        color: #999;
      }
    }

    /* 加载更多 */
    .loading-more {
      display: flex;
      justify-content: center;
      padding: 30px 0;
    }
  }

  /* 悬浮发帖按钮 */
  .float-post-btn {
    position: fixed;
    right: 30px;
    bottom: 100px;
    z-index: 100;

    .post-btn {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      background-color: #45e87f;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 12px rgba(69, 232, 127, 0.4);
      padding: 0;
      line-height: 1;

      &::after {
        border: none;
      }

      .icon-plus {
        font-size: 50px;
        color: #fff;
        font-weight: bold;
      }
    }
  }
}

/* 操作菜单样式 */
.action-sheet-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.action-sheet {
  width: 100%;
  background-color: #fff;
  border-radius: 20px 20px 0 0;
  overflow: hidden;

  .action-sheet-title {
    text-align: center;
    font-size: 28px;
    color: #999;
    padding: 30px 0;
    border-bottom: 1px solid #eee;
  }

  .action-sheet-item {
    text-align: center;
    font-size: 32px;
    color: #333;
    padding: 30px 0;
    border-bottom: 1px solid #eee;

    &:active {
      background-color: #f5f5f5;
    }
  }

  .action-sheet-cancel {
    text-align: center;
    font-size: 32px;
    color: #333;
    padding: 30px 0;
    margin-top: 10px;

    &:active {
      background-color: #f5f5f5;
    }
  }
}