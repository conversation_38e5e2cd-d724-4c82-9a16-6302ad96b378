.post-container {
  padding: 32px;
  background: #fff;
  max-height: 100vh;

  .vote-display {
    margin: 32px 0;
    padding: 24px;
    background: #f5f5f5;
    border-radius: 16px;

    .vote-title {
      font-size: 32px;
      font-weight: bold;
      margin-bottom: 24px;
    }

    .vote-options {
      .vote-option {
        display: block;
        padding: 16px;
        background: #fff;
        border-radius: 8px;
        margin-bottom: 16px;
        font-size: 28px;
      }
    }
  }

  .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 32px;

    .avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      margin-right: 16px;
    }

    .nickname {
      flex: 1;
      font-size: 32px;
      color: #333;
    }

    .publish-btn {
      background: #45e87f;
      color: #fff;
      border-radius: 20px;
      font-size: 28px;
    }
  }

  .title-section {
    position: relative;
    margin-bottom: 32px;
    background-color: #f5f5f5;
    border-radius: 16px;
    border: none;
    font-size: 32px;
    padding: 0 24px;

    .title-input {
      width: 100%;
      height: 88px;
      font-size: 32px;
    }

    .word-count {
      position: absolute;
      right: 24px;
      top: 50%;
      transform: translateY(-50%);
      color: #999;
      font-size: 28px;
    }
  }

  .template-tabs {
    white-space: nowrap;
    margin-bottom: 32px;

    .template-tab {
      display: inline-block;
      padding: 16px 32px;
      margin-right: 24px;
      font-size: 28px;
      color: #666;
      border-radius: 32px;
      background: #f5f5f5;

      &.active {
        background: rgba(69, 232, 127, 0.1);
        color: #45e87f;
      }
    }
  }

  .content-section {
    .content-textarea {
      width: 100%;
      height: 400px;
      padding: 24px;
      font-size: 28px;
      background: #f5f5f5;
      border-radius: 16px;
      box-sizing: border-box;
    }

    .discovery-form {
      .form-item {
        margin-bottom: 32px;

        .label {
          display: block;
          margin-bottom: 16px;
          font-size: 28px;
          color: #333;
        }

        input {
          width: 100%;
          height: 88px;
          padding: 0 24px;
          font-size: 28px;
          background: #f5f5f5;
          border-radius: 16px;
        }

        textarea {
          width: 100%;
          height: 200px;
          padding: 24px;
          font-size: 28px;
          background: #f5f5f5;
          border-radius: 16px;
          box-sizing: border-box;
        }

        .coin-select-btn {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 88px;
          padding: 0 32px;
          background: #f5f5f5;
          border-radius: 16px;
          transition: all 0.3s ease;
  
          .placeholder {
            color: #999;
            font-size: 28px;
          }
  
          .icon-arrow {
            width: 32px;
            height: 32px;
            background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'%3E%3Cpath d='M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z'/%3E%3C/svg%3E") no-repeat center;
            background-size: contain;
            transition: transform 0.3s ease;
          }
  
          &:active {
            background: #eee;
            transform: scale(0.98);
  
            .icon-arrow {
              transform: translateX(4px);
            }
          }
        }
      }
    }
  }

  .bottom-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: bottom 0.3s;

    .template-btn {
      margin: 0;
      padding: 0;

      .template-box {
        background-color: #f3f3f3;
        color: #000;
        width: 120rpx;
      }
    }

    .toolbar-btn-group {
      display: flex;
      align-items: center;
    }

    .toolbar-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      background: none;
      border: none;
      padding: 12px 20px;
      color: #666;
      font-size: 28px;
      border-radius: 32px;
      margin-right: 24px;
      transition: all 0.2s ease;

      &:last-child {
        margin-right: 0;
      }

      .icon-template,
      .icon-vote,
      .icon-coin,
      .icon-topic {
        margin-right: 8px;
        font-size: 32px;
      }

      &.active {
        background: rgba(69, 232, 127, 0.1);
        color: #45e87f;
      }

      &:active {
        opacity: 0.8;
        transform: scale(0.98);
      }

      &::after {
        border: none;
      }
    }

    .template-btn,
    .vote-btn,
    .coin-btn,
    .topic-btn {
      // display: flex;
      // align-items: center;
      // justify-content: center;
      background: none;
      border: none;
      // padding: 12px 20px;
      color: #666;
      font-size: 28px;
      // border-radius: 32px;
      // margin-right: 24px;
      transition: all 0.2s ease;

      &:last-child {
        margin-right: 0;
      }

      .icon-template,
      .icon-vote,
      .icon-coin,
      .icon-topic {
        margin-right: 8px;
        font-size: 32px;
      }

      &.active {
        background: rgba(69, 232, 127, 0.1);
        color: #45e87f;
      }

      &:active {
        opacity: 0.8;
        transform: scale(0.98);
      }

      &::after {
        border: none;
      }
    }
  }

  .bottom-fixed {
    position: fixed;
    left: 0;
    right: 0;
    background: #fff;
    padding: 10px 32px;
    z-index: 999;
  }

  .template-popup {
    position: fixed;
    left: 0;
    right: 0;
    top: 50%;
    bottom: 0;
    z-index: 1000;

    .popup-mask {
      position: fixed;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
    }

    .popup-content {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      background: #fff;
      border-radius: 24px 24px 0 0;
      padding: 32px;
      animation: slideUp 0.3s ease-out;

      .popup-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 32px;
        font-size: 32px;

        .close-btn {
          font-size: 48px;
          color: #999;
          padding: 0 16px;
        }
      }

      .template-list {
        display: flex;
        overflow-x: auto;
        padding-bottom: 32px;
        
        // 隐藏滚动条但保持可滚动
        &::-webkit-scrollbar {
          display: none;
        }

        .template-item {
          flex: 0 0 400px;
          margin-right: 24px;
          height: 500px;
          padding: 24px;
          background: #f5f5f5;
          border-radius: 16px;
          font-size: 28px;
          color: #333;
          
          // 临时的demo展示区样式
          .demo-area {
            width: 100%;
            height: 400px;
            background: #fff;
            border-radius: 12px;
            margin-top: 16px;
          }

          &.active {
            background: rgba(69, 232, 127, 0.1);
            color: #45e87f;
          }

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
  }

  .vote-popup {
    position: fixed;
    left: 0;
    right: 0;
    top: 50%;
    bottom: 0;
    z-index: 1000;

    .popup-mask {
      position: fixed;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
    }

    .popup-content {
      // 与模板弹窗类似的样式
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      background: #fff;
      border-radius: 24px 24px 0 0;
      padding: 32px;
      animation: slideUp 0.3s ease-out;

      .popup-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 32px;
      
        Text {
          font-size: 32px;
        }
      
        .header-btns {
          display: flex;
          gap: 16px;
      
          Button {
            font-size: 28px;
            padding: 8px 24px;
            border-radius: 32px;
      
            &.create-btn {
              background: #45e87f;
              color: #fff;
              border-radius: 20rpx;
            }
      
            &.close-btn {
              background: #f5f5f5;
              color: #666;
              border-radius: 20rpx;
            }
      
            &::after {
              border: none;
            }
          }
        }
      }

      .vote-title-input {
        background-color: #f5f5f5;
        border-radius: 16rpx;
        border: none;
        font-size: 32rpx;
        padding: 24rpx;
        margin-bottom: 20px;

        .word-count {
          position: absolute;
          right: 24px;
          top: 50%;
          transform: translateY(-50%);
          color: #999;
          font-size: 28px;
        }
      }
    }
  
    .vote-options-list {
      .option-item {
        position: relative;
        margin-bottom: 16px;
      
        .option-input {
          width: 100%;
          height: 88px;
          padding: 0 60px 0 24px;
          font-size: 32px;
          background: #f5f5f5;
          border-radius: 16px;
        }
      
        .delete-btn {
          position: absolute;
          z-index: 10;
          right: 24px;
          top: 50%;
          transform: translateY(-50%);
          width: 40px;
          height: 40px;
          line-height: 40px;
          text-align: center;
          font-size: 32px;
          color: #999;
        }
      }
    }
    
    .add-option-btn {
      width: 100%;
      height: 88px;
      background: rgba(69, 232, 127, 0.1);
      color: #45e87f;
      font-size: 28px;
      border-radius: 16px;
      margin-top: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      
      &:active {
        opacity: 0.8;
        transform: scale(0.98);
      }
      
      &::after {
        border: none;
      }
    }
  }

  .coin-popup,
  .topic-popup {
    position: fixed;
    left: 0;
    right: 0;
    top: 50%;
    bottom: 0;
    z-index: 1000;

    .popup-mask {
      position: fixed;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
    }

    .popup-content {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      .coin-popup {
        .popup-content {
          display: flex;
          flex-direction: column;
          height: 80vh;
      
          .coin-header {
            flex-shrink: 0;
          }
      
          .search-results {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
      
            .results-title {
              flex-shrink: 0;
            }
      
            .results-list {
              flex: 1;
              overflow-y: auto;
            }
          }
        }
      }
      background: #fff;
      border-radius: 24px 24px 0 0;
      padding: 32px;
      overflow-y: auto;
      box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.1);
      animation: slideUp 0.3s ease-out;
  }

    @keyframes slideUp {
      from {
        transform: translateY(100%);
      }
      to {
        transform: translateY(0);
      }
    }
    .coin-header,
    .topic-header {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 32px;
      border-bottom: 1px solid #eee;
      padding-bottom: 16px;

      .search-box {
        flex: 1;
        display: flex;
        align-items: center;
        background: #f5f5f5;
        border-radius: 32px;
        padding: 0 24px;

        .icon-search {
          color: #999;
          margin-right: 8px;
        }

        .search-input {
          flex: 1;
          height: 72px;
          font-size: 28px;
        }
      }
      .create-topic-btn {
        height: 72px;
        line-height: 72px;
        padding: 0 30px;
        background: #45e87f;
        color: #fff;
        border-radius: 36px;
        font-size: 28px;
      }

      .cancel-btn {
        padding: 16px 0 16px 32px;
        font-size: 28px;
        color: #666;
        background: none;

        &::after {
          border: none;
        }
      }
    }

    .coin-list,
    .topic-list {
      max-height: 60vh;
      overflow-y: auto;

      .coin-item,
      .topic-item {
        display: flex;
        align-items: center;
        padding: 32px 0;
        border-bottom: 1px solid #eee;

        .coin-name,
        .topic-name {
          font-size: 32px;
          font-weight: bold;
          margin-right: 16px;
        }

        .coin-fullname,
        .topic-description {
          font-size: 28px;
          color: #666;
        }
      }
    }
    
    .search-results {
      .results-title {
        font-size: 32px;
        font-weight: bold;
        margin-bottom: 16px;
        color: #333;
      }

      .results-list {
        max-height: 60vh;
        overflow-y: auto;

        .result-item,
        .topic-item {
          display: flex;
          align-items: center;
          padding: 24px;
          border-bottom: 1px solid #f0f0f0;
          position: relative;
          background: #fff;
          border-radius: 12px;
          margin-bottom: 12px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
          transition: all 0.2s ease;
          overflow: hidden;
          
          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 6px;
            background: linear-gradient(to bottom, #45e87f, #3ad178);
            opacity: 0;
            transition: opacity 0.2s ease;
          }
          
          &:active {
            transform: scale(0.98);
            background: #f9f9f9;
            
            &::before {
              opacity: 1;
            }
          }
          
          .coin-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, #45e87f, #3ad178);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            color: white;
            font-weight: bold;
            font-size: 24px;
          }

          .coin-info {
            flex: 1;
          }

          .coin-symbol {
            font-size: 32px;
            font-weight: bold;
            // margin-bottom: 8px;
            color: #333;
          }

          .coin-name {
            font-size: 28px;
            color: #666;
          }
          
          .select-indicator {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 2px solid #eee;
            margin-left: 16px;
            transition: all 0.2s ease;
            opacity: 0.5;
            
            &.selected {
              background: #45e87f;
              border-color: #45e87f;
              opacity: 1;
            }
          }
        }

        .no-results {
          padding: 48px 0;
          text-align: center;
          color: #999;
          font-size: 28px;
        }
      }
    }
  }

  /* 创建话题弹窗样式 */
  .topic-creator-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1100;
    display: flex;
    align-items: flex-end;

    .topic-creator {
      width: 100%;
      background: #fff;
      border-radius: 32px 32px 0 0;
      padding: 32px;

      .creator-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 32px;

        Text {
          font-size: 32px;
          font-weight: bold;
        }

        .close {
          font-size: 40px;
          color: #999;
        }
      }

      .creator-content {
        .input-group {
          margin-bottom: 24px;

          .label {
            font-size: 28px;
            color: #666;
            margin-bottom: 12px;
            display: block;
          }

          .title-input {
            width: 100%;
            height: 88px;
            background: #f5f5f5;
            border-radius: 16px;
            padding: 0 24px;
            font-size: 28px;
            box-sizing: border-box;
          }

          .desc-input {
            width: 100%;
            height: 200px;
            background: #f5f5f5;
            border-radius: 16px;
            padding: 24px;
            font-size: 28px;
            box-sizing: border-box;
          }

          .word-count {
            text-align: right;
            font-size: 24px;
            color: #999;
            margin-top: 8px;
          }
        }
      }

      .create-btn {
        width: 100%;
        height: 88px;
        background: #ccc;
        color: #fff;
        border-radius: 44px;
        font-size: 32px;
        margin-top: 32px;

        &.active {
          background: #45e87f;
        }
      }
    }
  }

  .selected-tags {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 32px;
    margin-top: 16px;

    .coin-tag,
    .topic-tag {
      display: inline-block;
      padding: 8px 16px;
      margin-right: 16px;
      margin-bottom: 16px;
      font-size: 24px;
      border-radius: 32px;
    }

    .coin-tag {
      color: #ff9500;
      background: rgba(255, 149, 0, 0.1);
    }

    .topic-tag {
      color: #007aff;
      background: rgba(0, 122, 255, 0.1);
    }
  }
  
  /* 不懂就问提示弹窗样式 */
  .ask-tips-container {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 150px;
    z-index: 900;
    padding: 0 32px;
  }
  
  .ask-tips-box {
    background-color: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    animation: fadeIn 0.3s ease-out;
  }
  
  .ask-tips-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
    font-size: 30px;
    font-weight: bold;
    
    .close-icon {
      font-size: 40px;
      color: #999;
      padding: 0 8px;
    }
  }
  
  .ask-tips-content {
    padding: 16px 24px;
    
    .tip-item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .tip-icon {
        margin-right: 16px;
        font-size: 32px;
      }
      
      .tip-text {
        font-size: 28px;
        color: #333;
        flex: 1;
      }
    }
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* 社区公约弹窗样式 */
  .community-rules-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 32px;
  }
  
  .community-rules-popup {
    background: #fff;
    border-radius: 16px;
    width: 100%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    animation: fadeIn 0.3s ease-out;
  }
  
  .rules-header {
    padding: 32px 32px 16px 32px;
    text-align: center;
    border-bottom: 1px solid #f0f0f0;
    
    .rules-title {
      font-size: 36px;
      font-weight: bold;
      color: #333;
    }
  }
  
  .rules-content {
    padding: 32px;
    max-height: 50vh;
    overflow-y: auto;
    
    .rules-text {
      font-size: 28px;
      line-height: 1.6;
      color: #333;
      white-space: pre-line;
    }
  }
  
  .rules-footer {
    padding: 16px 32px 32px 32px;
    
    .confirm-btn {
      width: 100%;
      height: 88px;
      background: #45e87f;
      color: #fff;
      border-radius: 44px;
      font-size: 32px;
      border: none;
    }
  }
  
}