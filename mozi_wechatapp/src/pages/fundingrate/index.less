.pcrBox {
  background-color: #efefef;
  // padding: 20px;
  box-sizing: border-box;
  min-height: 100vh;

  .pcrTab {
    background-color: #fff;

    .adm-tab-bar-item {
      border-bottom: 1px solid transparent;
    }

    .adm-tab-bar-item-active {
      border-bottom: 1px solid #45e87f;
    }
  }

  .currentPCR {
    padding: 20px;
    background-color: #fff;
    margin-bottom: 20px;
  
    .pickerList {
      display: flex;
      justify-content: space-around;
      height: 50px;

      .picker-item {
        display: flex;
        margin-left: 10px;
  
        .picker-title {
          font-weight: bold;
          margin-right: 10px;
        }
      }

      // .pickerSelect {
      //   display: flex;
      //   align-items: center;
      //   height: 50px;
      
      //   .selectIcon {
      //     margin-right: 10px;
      //   }
      // }
    }

    .header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }

    .currentPCRChart {
      width: 100%;
      // height: 600px;

      .scroll {
        width: 100%;
      }

      .show-more-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        
        .more {
          margin-right: 10px;
        }
      }

      .list-item {
        padding-left: 0;

        .adm-list-item-content-main {
          padding: 0;
        }
      }

      .fund-list {
        display: flex;
        // flex-direction: row;
        // width: 100%;
        

        .fund-item {
          background-color: #eff2f5;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 20px 10px;
          min-width: 150px;
  
          .fund-url {
            height: 36px;
            width: 36px;
            margin-right: 10px;
          }
        }

        

        .fund-item-first {
          min-width: 200px;
        }

        .green {
          color: #02c076;
        }

        .red {
          color: #ff3333;
        }
      }

      .fund-title {

        .fund-item {
          border-bottom: 1px solid rgba(128, 128, 128, 0.2);
        }
      }

      .fund-detail-box {
        height: 900px;
        overflow-y: hidden;
      }

      .show-more-true {
        height: auto;
      }

      .list-detail {
       

        .fund-item {
          background-color: #fff;
        }
      }

      
    }

    .currentChart {
      width: 100%;
      height: 600px;
      position: relative;

      .chart-arrawsalt {
        position: absolute;
        right: 90px;
        bottom: 85px;
        background-color: #d5d5d5;
        border-radius: 50%;
        padding: 6px;
        box-sizing: border-box;
        z-index: 100;
      }
    }

  }


  
}
