.me {
  background: #EEF0F3 url('@/assets/image/me-bg.png') no-repeat top center;
  background-size: 100% 460px;
  min-height: 100vh;
  padding: 0 24px;

  .header {
    background: transparent;
    // height: 300px;
    margin-bottom: 20px;
    padding: 80px 0 20px;

    .loginBox {
      background-color: transparent;
    }
    
    .loginBox::after {
      border: none;
    }

    .headerUser {
      display: flex;
      padding: 10px 14px;
      align-items: center;
      margin-bottom: 50px;

      .headerAvatar {
        width: 90px;
        height: 90px;
        border: 1px solid transparent;
        border-radius: 50%;
        margin-right: 40px;
      }
    }

    // 功能按钮区域
    .actionButtons {
      background: #fff;
      border-radius: 16px;
      margin: 40px 0 0;
      padding: 30px 20px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      display: flex;
      justify-content: space-around;

      .actionButton {
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;

        .actionIcon {
          width: 80px;
          height: 80px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 12px;
          transition: all 0.3s ease;

          &:active {
            transform: scale(0.95);
          }

          .actionIconImg {
            width: 56px;
            height: 56px;
            object-fit: contain;
          }
        }

        .actionText {
          color: #333;
          font-size: 24px;
          font-weight: 500;
        }
      }
    }
  }

  // 第二排功能按钮
  .secondaryActions {
    background: #fff;
    margin: 20px 0;
    border-radius: 16px;
    padding: 30px 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

    .actionRow {
      display: flex;
      justify-content: space-around;

      .actionButton {
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;

        .actionIcon {
          &.secondary {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            position: relative;

            .actionIconImg {
              &.secondary {
                width: 44px;
                height: 44px;
                object-fit: contain;
              }
            }
            transition: all 0.3s ease;

            &:active {
              transform: scale(0.95);
            }

            .badge {
              position: absolute;
              top: -5px;
              right: -5px;
              background: #ff4757;
              color: #fff;
              border-radius: 50%;
              width: 20px;
              height: 20px;
              font-size: 20px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-weight: bold;
            }
          }
        }

        .actionText {
          &.secondary {
            color: #333;
            font-size: 24px;
            font-weight: 400;
          }
        }
      }
    }
  }

  // 积分模块
  .pointsSection {
    background: url('@/assets/image/integral.png') no-repeat center center;
    background-size: 100% 100%;
    margin: 20px 0;
    border-radius: 16px;
    padding: 30px 20px;
    // box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 180px;
    max-height: 210px;
    position: relative;

    .pointsInfo {
      flex: 1;
      padding-left: 26px;

      .pointsTitle {
        color: #333;
        font-size: 28px;
        font-weight: 500;
        margin-bottom: 10px;
        display: block;
      }

      .pointsValueRow {
        display: flex;
        align-items: baseline;
        margin-bottom: 10px;
      }

      .pointsValue {
        color: #52c41a;
        font-size: 50px;
        font-weight: bold;
        margin-bottom: 10px;
        display: block;
      }

      .pointsDaily {
        color: #707070;
        font-size: 22px;
        margin-left: 20px;
        display: block;
      }

      .pointsRank {
        color: #707070;
        font-size: 22px;
        margin-bottom: 5px;
        display: block;
      }
    }

    .pointsAction {
      position: absolute;
      top: 20px;
      right: 30px;
      display: flex;
      align-items: center;
      
      .pointsButton {
        color: #fff;
        border-radius: 20px;
        padding: 12px 10px 12px 24px;
        font-size: 24px;
        cursor: pointer;
        font-family: PingFang SC;
        font-weight: 500;
        line-height: 36px;
      }
    }

    .pointsCoin {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 80px;
      height: 110px;
    }
  }

  // 左右分布的按钮
  .horizontalButtons {
    margin: 20px 0;
    display: flex;
    gap: 12px;

    .horizontalBtn {
      flex: 1;
      background: #fff;
      border-radius: 20px;
      padding: 20px 16px;
      box-shadow: 0 1px 8px rgba(0, 0, 0, 0.06);
      display: flex;
      flex-direction: column;
      position: relative;
      cursor: pointer;
      border: none;
      text-align: left;
      border: 1px solid #f0f0f0;

      &::after {
        border: none;
      }

      .btnIcon {
        width: 48px;
        height: 48px;
        background: #f8f9fa;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 12px;
        align-self: flex-start;
        margin-left: 26px;

        .btnIconImg {
          width: 44px;
          height: 44px;
        }
      }

      .btnBottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
      }

      .btnContent {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        min-width: 0;
        padding-left: 26px;
      }

      .btnText {
        color: #333;
        font-size: 28px;
        font-weight: 500;
        line-height: 1.2;
        margin-bottom: 4px;
        text-align: left;
      }

      .btnSubtext {
        color: #999;
        font-size: 22px;
        line-height: 1.3;
        text-align: left;
      }

      .btnArrow {
        flex-shrink: 0;
        margin-left: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 60px; // 给箭头容器一个高度来匹配文字区域的高度
      }

      &:active {
        background: #f8f8f8;
        transform: scale(0.98);
      }
    }
  }

  .footer {
    margin: 20px 0;
    background: #fff;
    border-radius: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

    .footerList {
      background-color: transparent;

      .footerItem {
        border-bottom: none;
  
        .footerBtn {
          border: none;
          display: flex;
          justify-content: space-between;
          width: 100%;
          height: 60px;
          align-items: center;
          background-color: transparent;
          padding: 0 24px;
          text-align: left;
        }
        
        .footerBtn::after {
          border: none;
        }

        .icon {
          width: 48px;
          display: flex;
          align-items: center;
          justify-content: flex-start;
        }

        .text {
          flex: 1;
          margin-left: 20px;
          font-size: 28px;
          color: #333;
          font-weight: 500;
        }

        .extra {
          width: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #A5A9AF;
        }
      }
  
      .last {
        border: none;
      }
    }

  }

  .logoutBtn {
    margin: 32px 0 20px 0;
    background: #dc3545;
    border-radius: 20px;
    height: 90px;
    color: #fff;
    font-size: 32px;
    font-weight: 500;
    border: none;
    box-shadow: 0 2px 12px rgba(220, 53, 69, 0.3);

    &::after {
      border: none;
    }

    &:active {
      background: #c82333;
      transform: scale(0.98);
    }
  }

  .popContainer {
    padding: 20px;
    box-sizing: border-box;

    .aboutItem {
      margin-bottom: 10px;
      font-size: 26rpx;

      text {
        font-weight: bold;
      }
    }
    .sec-desc {
      margin-top: 20px;
    }

    .sec-con {
      margin-top: 20px;
    }

    .score-desc {
      display: flex;
      justify-content: space-between;
      font-size: 22rpx;
      margin-top: 10rpx;
      color: #898989;
    }
    .scoreList {
      margin-top: 20px;

      .scoreItem {
        background-color: #efefef;
        width: 60px;
        height: 60px;
        text-align: center;
        line-height: 60px;
        border-radius: 10px;
        color: #000;
      }

      .scoreActive {
        background-color: #45e87f;
        color: #fff;
      }
    }

    .score-con {
      margin-top: 20px;

      .score-con-desc {
        color: #898989;
        font-size: 24px;
      }

      .score-text {
        box-sizing: border-box;
        width: 100%;
        height: 200px;
        // background-color: #efefef;
        border: 1px solid #efefef;
        margin-top: 10px;
        padding: 10px;
      }
    }

    .score-btn {
      background-color: #02c076;
      color: #fff;
      margin-top: 20px;
    }

    .score-btn-disable {
      background-color: #f8f8f8;
      color: #00000040;
    }

    

    .contactTitle {
      margin-bottom: 16px;
      display: block;
    }

    .contactEmail {
      display: flex;
      align-items: center;

      .contactCopy {
        margin-left: 20px;
      }
    }

    .attendPic {
      width: 100%;
    }
  }

  .scrollContainer {
    padding: 20px;
    box-sizing: border-box;
    // height: 80vh;

    .rewardBox {
      font-size: 20px;
      display: inline-block;
      width: 80%;
    }

    .contactTitle {
      margin-bottom: 20px;
      display: block;
    }

    .attendPic {
      width: 100%;
    }

    .contactEmail {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-right: 20px;
      // flex-wrap: wrap;

      .coin-key {
        word-wrap: break-word;
        word-break: break-all;
        white-space: normal;
      }

      .contactCopy {
        margin-left: 20px;
      }
    }
  }
  
  .contactContainer {
    padding: 30px 20px;
  }
}

// 日历组件样式
.calendarSection {
  margin-bottom: 20px;
}
