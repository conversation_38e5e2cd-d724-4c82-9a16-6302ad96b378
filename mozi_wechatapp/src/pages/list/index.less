.findPage {
  // padding: 0 20px;
  background-color: #efefef;

  .tabContainer {
    // margin-bottom: 20px;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    z-index: 100;
    height: 60px;

    .adm-tab-bar-item {
      color: #000;
    }

    
    .adm-tab-bar-item-active {
      color: #45e87f;
    }


  }


  .scroll {
    height: 100vh;
    // padding-top: 60rpx;
    box-sizing: border-box;
    position: relative;

    .gridTitle {
      position: fixed;
      top: 0;
      background-color: #dfdfdf;
      z-index: 10;
      align-items: center;
    }

    .list {
      margin-top: 50px;
    }
  }
}

// .rank {
//   border: 1px solid transparent;
//   background-color: #fff;
//   border-radius: 4px;
// }

.rankTitle {
  display: flex;
  align-items: center;

  .rankTitleTime {
    font-size: 20px;
    color: #898989;
    margin-left: 10px;
  }
}

.gridText {
  display: flex;
  align-items: center;
}