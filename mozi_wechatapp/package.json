{"name": "mozi_we<PERSON>tapp", "version": "1.0.0", "private": true, "description": "mozi_we<PERSON>tapp", "templateInfo": {"name": "default", "typescript": false, "css": "Less", "framework": "React"}, "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "build:harmony-hybrid": "taro build --type harmony-hybrid", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "dev:harmony-hybrid": "npm run build:harmony-hybrid -- --watch", "test": "jest", "lint:fix": "eslint --fix ./src"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/plugin-transform-react-jsx": "^7.24.6", "@babel/runtime": "^7.21.5", "@tarojs/components": "3.6.29", "@tarojs/helper": "3.6.29", "@tarojs/plugin-framework-react": "3.6.29", "@tarojs/plugin-html": "^3.6.29", "@tarojs/plugin-platform-alipay": "3.6.29", "@tarojs/plugin-platform-h5": "3.6.29", "@tarojs/plugin-platform-harmony-hybrid": "3.6.29", "@tarojs/plugin-platform-jd": "3.6.29", "@tarojs/plugin-platform-qq": "3.6.29", "@tarojs/plugin-platform-swan": "3.6.29", "@tarojs/plugin-platform-tt": "3.6.29", "@tarojs/plugin-platform-weapp": "3.6.29", "@tarojs/react": "3.6.29", "@tarojs/runtime": "3.6.29", "@tarojs/shared": "3.6.29", "@tarojs/taro": "3.6.29", "antd-mobile": "^5.36.0", "echarts": "^5.5.1", "lodash": "^4.17.21", "react": "^18.0.0", "react-dom": "^18.0.0", "react-markdown": "^9.0.1"}, "devDependencies": {"@babel/core": "^7.8.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@tarojs/cli": "3.6.29", "@tarojs/taro-loader": "3.6.29", "@tarojs/test-utils-react": "^0.1.1", "@tarojs/webpack5-runner": "3.6.29", "@types/jest": "^29.3.1", "@types/node": "^18.15.11", "@types/react": "^18.0.0", "@types/webpack-env": "^1.13.6", "babel-preset-taro": "3.6.29", "eslint": "^8.12.0", "eslint-config-taro": "3.6.29", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "jest": "^29.3.1", "jest-environment-jsdom": "^29.5.0", "postcss": "^8.4.18", "react-refresh": "^0.11.0", "stylelint": "^14.4.0", "taro-iconfont-cli": "^3.3.0", "ts-node": "^10.9.1", "webpack": "5.78.0", "webpack-bundle-analyzer": "^4.10.2"}}