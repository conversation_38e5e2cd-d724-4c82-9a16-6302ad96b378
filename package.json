{"name": "mozi-uniapp", "version": "1.0.0", "description": "Mozi UniApp Project", "scripts": {"dev:h5": "uni", "build:h5": "uni build", "dev:mp-weixin": "uni -p mp-weixin", "build:mp-weixin": "uni build -p mp-weixin", "wx:dev": "npm run dev:mp-weixin", "wx:open": "echo '请在微信开发者工具中打开: $(pwd)/dist/dev/mp-weixin' && open -a 'wechatwebdevtools' '$(pwd)/dist/dev/mp-weixin' 2>/dev/null || echo '请手动打开微信开发者工具'", "wx:auto": "npm run wx:dev & sleep 3 && npm run wx:open"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-alpha-4080120250821001", "@dcloudio/uni-h5": "3.0.0-alpha-4080120250821001", "@dcloudio/uni-mp-weixin": "3.0.0-alpha-4080120250821001", "clipboard": "^2.0.11", "dayjs": "^1.11.14", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.5.0", "uview-plus": "^3.5.20", "vue": "^3.4.21"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/vite-plugin-uni": "3.0.0-alpha-4080120250821001", "@uni-helper/vite-plugin-uni-pages": "^0.2.20", "sass": "^1.91.0", "sass-loader": "^16.0.5", "typescript": "^5.4.0", "vite": "5.2.8"}}