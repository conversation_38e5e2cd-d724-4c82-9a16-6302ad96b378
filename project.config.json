{"description": "Mozi UniApp - 自动编译微信小程序", "setting": {"es6": true, "postcss": true, "minified": true, "uglifyFileName": false, "enhance": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useCompilerPlugins": false, "minifyWXML": true, "compileWorklet": false, "uploadWithSourceMap": true, "packNpmManually": false, "minifyWXSS": true, "localPlugins": false, "disableUseStrict": false, "condition": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "miniprogramRoot": "dist/dev/mp-weixin/", "simulatorPluginLibVersion": {}, "packOptions": {"ignore": [], "include": []}, "scripts": {"beforeCompile": "npm run dev:mp-weixin", "beforePreview": "npm run build:mp-weixin"}, "appid": "wxa7bc6cc1c14e052a", "projectname": "mozi-uniapp", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "libVersion": "3.9.2"}